# Chat Participant Debug Guide

## Why <PERSON><PERSON> switches to "Ask" mode

This happens when VS Code doesn't recognize `@deepagent` as a valid chat participant. Here are the fixes we implemented:

### 1. ✅ Fixed API Proposals
Changed from deprecated APIs to current ones:
```json
"enabledApiProposals": [
  "chatParticipantAdditions",
  "languageModelSystem"
]
```

### 2. ✅ Added Activation Events
```json
"activationEvents": [
  "onLanguage:*"
]
```

### 3. ✅ Enhanced Registration
- Better error handling during activation
- Added followup provider for better UX
- More detailed logging

## How to Test

1. **Launch Extension Development Host** (F5)
2. **Check Console** (F12 → Console tab) for:
   ```
   🚀 Multi-Agent Orchestrator: Extension activating...
   ✅ Multi-Agent Orchestrator: Chat participant registered as @deepagent
   ```
3. **Open Copilot Chat** (Ctrl+Alt+I)
4. **Type `@`** - you should see `deepagent` in the dropdown
5. **Type `@deepagent hello`** - should trigger the handler

## Troubleshooting

### If @deepagent doesn't appear in dropdown:
- Check console for activation errors
- Restart Extension Development Host
- Check VS Code version (needs 1.85.0+)

### If it still switches to "Ask" mode:
- The participant ID might conflict
- Extension might not be properly activated
- API proposals might not be enabled

### Fallback Test:
Try typing `@deepagent` and pressing Enter even if it's not in the dropdown - sometimes it still works!
