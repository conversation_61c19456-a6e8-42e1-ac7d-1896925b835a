import * as vscode from 'vscode';
import * as net from 'net';
import { getModel } from './models';
import { createPlan } from './planner';
import { runStep } from './executor';

interface IPCRequest {
  id: string;
  method: 'plan' | 'execute' | 'planAndExecute';
  params: any;
}

interface IPCResponse {
  id: string;
  result?: any;
  error?: string;
}

export class VSCodeIPCBridge {
  private server: net.Server | null = null;
  private clients: Set<net.Socket> = new Set();

  async start(): Promise<void> {
    return new Promise((resolve, reject) => {
      this.server = net.createServer((socket) => {
        console.log('🔗 MCP client connected to IPC bridge');
        this.clients.add(socket);

        socket.on('data', async (data) => {
          try {
            const lines = data.toString().split('\n').filter(line => line.trim());
            
            for (const line of lines) {
              const request: IPCRequest = JSON.parse(line);
              console.log('📨 Received IPC request:', request.method, request.params);
              
              const response = await this.handleRequest(request);
              socket.write(JSON.stringify(response) + '\n');
            }
          } catch (error) {
            console.error('❌ Error processing IPC request:', error);
            const errorResponse: IPCResponse = {
              id: 'unknown',
              error: error instanceof Error ? error.message : String(error)
            };
            socket.write(JSON.stringify(errorResponse) + '\n');
          }
        });

        socket.on('close', () => {
          console.log('🔌 MCP client disconnected from IPC bridge');
          this.clients.delete(socket);
        });

        socket.on('error', (err) => {
          console.error('❌ IPC socket error:', err);
          this.clients.delete(socket);
        });
      });

      this.server.listen(3002, 'localhost', () => {
        console.log('🌐 VS Code IPC bridge listening on port 3002');
        resolve();
      });

      this.server.on('error', (err) => {
        console.error('❌ IPC server error:', err);
        reject(err);
      });
    });
  }

  private async handleRequest(request: IPCRequest): Promise<IPCResponse> {
    try {
      let result: any;

      switch (request.method) {
        case 'plan':
          result = await this.handlePlan(request.params.task);
          break;

        case 'execute':
          result = await this.handleExecute(request.params.step, request.params.context);
          break;

        case 'planAndExecute':
          result = await this.handlePlanAndExecute(request.params.task, request.params.maxSteps);
          break;

        default:
          throw new Error(`Unknown method: ${request.method}`);
      }

      return {
        id: request.id,
        result
      };
    } catch (error) {
      return {
        id: request.id,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  private async handlePlan(task: string): Promise<string[]> {
    console.log('🧠 IPC: Creating plan for task:', task);

    try {
      // Get Opus model for planning
      console.log('🔍 IPC: Getting Claude Opus model...');
      const opus = await getModel('claude-opus-4');
      console.log('✅ IPC: Got Claude Opus model');

      // Create the plan using the same code as the chat participant
      console.log('📋 IPC: Calling createPlan function...');
      const startTime = Date.now();
      const plan = await createPlan(opus, task, new vscode.CancellationTokenSource().token);
      const duration = Date.now() - startTime;

      console.log(`✅ IPC: Plan created with ${plan.length} steps in ${duration}ms`);
      return plan;
    } catch (error) {
      console.error('❌ IPC: Error creating plan:', error);
      throw error;
    }
  }

  private async handleExecute(step: string, context?: string): Promise<string> {
    console.log('⚡ IPC: Executing step:', step);

    try {
      // Get Sonnet model for execution
      console.log('🔍 IPC: Getting Claude Sonnet model...');
      const sonnet = await getModel('claude-sonnet-4');
      console.log('✅ IPC: Got Claude Sonnet model');

      // Create a proper stream to capture output
      let output = '';
      const captureStream = {
        markdown: (text: string) => {
          console.log('📝 IPC: Capturing markdown:', text.substring(0, 100) + '...');
          output += text;
        },
        button: () => {},
        filetree: () => {},
        anchor: () => {},
        progress: () => {},
        reference: () => {},
        push: () => {}
      };

      // Create a minimal chat context
      const mockChatContext = {
        history: []
      } as vscode.ChatContext;

      console.log('🚀 IPC: Executing step with runStep function...');
      const startTime = Date.now();

      // Execute the step using the same code as the chat participant
      await runStep(step, mockChatContext, sonnet, captureStream as any, new vscode.CancellationTokenSource().token);

      const duration = Date.now() - startTime;
      console.log(`✅ IPC: Step execution completed in ${duration}ms, captured ${output.length} characters`);

      return output || `Executed step: ${step}${context ? `\nContext: ${context}` : ''}`;
    } catch (error) {
      console.error('❌ IPC: Error executing step:', error);
      throw error;
    }
  }

  private async handlePlanAndExecute(task: string, maxSteps: number = 5): Promise<string> {
    console.log('🚀 IPC: Planning and executing task:', task);
    
    // Step 1: Create plan
    const plan = await this.handlePlan(task);
    
    let result = `## Plan and Execution for: ${task}\n\n### Plan:\n`;
    result += plan.map((step, index) => `${index + 1}. ${step}`).join('\n');
    result += '\n\n### Execution:\n\n';
    
    // Step 2: Execute each step (up to maxSteps)
    const stepsToExecute = plan.slice(0, maxSteps);
    
    for (let i = 0; i < stepsToExecute.length; i++) {
      const step = stepsToExecute[i];
      result += `#### Step ${i + 1}: ${step}\n`;
      
      try {
        const stepOutput = await this.handleExecute(step);
        result += stepOutput + '\n\n';
      } catch (stepError) {
        result += `❌ Error executing step: ${stepError instanceof Error ? stepError.message : String(stepError)}\n\n`;
      }
    }
    
    if (plan.length > maxSteps) {
      result += `\n*Note: Only executed first ${maxSteps} of ${plan.length} steps. Remaining steps can be executed separately.*\n`;
    }
    
    return result;
  }

  async stop(): Promise<void> {
    if (this.server) {
      // Close all client connections
      for (const client of this.clients) {
        client.end();
      }
      this.clients.clear();

      // Close the server
      return new Promise((resolve) => {
        this.server!.close(() => {
          console.log('🛑 VS Code IPC bridge stopped');
          resolve();
        });
      });
    }
  }
}
