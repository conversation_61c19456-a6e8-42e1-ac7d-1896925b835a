import * as vscode from 'vscode';

export async function getModel(id: string): Promise<vscode.LanguageModelChat> {
  console.log(`🔍 Requesting model: ${id}`);
  
  try {
    console.log(`📡 Selecting chat models with vendor: copilot, family: ${id}`);

    const models = await vscode.lm.selectChatModels({ vendor: 'copilot', family: id });
    console.log(`📋 Found ${models.length} models with specific family filter`);
    
    // First try to find in the filtered models
    let m = models.find(x => x.id === id);
    
    // If not found, try in all models
    if (!m) {
      console.log('🔍 Not found in filtered models, checking all models...');

      // Also try without family filter to see ALL available models
      const allModels = await vscode.lm.selectChatModels({ vendor: 'copilot' });
      console.log(`📋 Found ${allModels.length} total models from copilot vendor`);
      
      // Print each model individually for detailed debugging
      console.log('\n🔍 ALL AVAILABLE MODELS:');
      allModels.forEach((model, index) => {
        console.log(`🔍 Model ${index + 1}:`);
        console.log(`   - ID: ${model.id}`);
        console.log(`   - Name: ${model.name}`);
        console.log(`   - Vendor: ${model.vendor}`);
        console.log(`   - Family: ${model.family}`);
        console.log(`   - Version: ${model.version || 'N/A'}`);
        console.log(`   - Max Input Tokens: ${model.maxInputTokens || 'N/A'}`);
        console.log('   ---');
      });
      
      console.log(`🎯 Looking for model with ID: "${id}"`);
      
      m = allModels.find(x => x.id === id);

      if (!m) { 
        console.error(`❌ Model ${id} not found in any available models`);
        throw new Error(`Model ${id} unavailable. Please configure Copilot or BYOM model.`); 
      }
    }
    
    console.log(`✅ Model found: ${m.id} (${m.name})`);
    return m;
  } catch (error) {
    console.error('❌ Error in getModel:', error);
    
    // Try a fallback approach - get any Claude model available
    try {
      console.log('🔄 Attempting fallback: searching for any Claude model...');
      const fallbackModels = await vscode.lm.selectChatModels();
      const claudeModels = fallbackModels.filter(m => 
        m.family?.toLowerCase().includes('claude') || 
        m.id?.toLowerCase().includes('claude') ||
        m.name?.toLowerCase().includes('claude')
      );
      
      console.log('🔍 Found Claude models:', claudeModels.map(m => ({ id: m.id, name: m.name, family: m.family })));
      
      if (claudeModels.length > 0) {
        console.log(`🔄 Using fallback Claude model: ${claudeModels[0].id}`);
        return claudeModels[0];
      }
    } catch (fallbackError) {
      console.error('❌ Fallback also failed:', fallbackError);
    }
    
    throw error;
  }
}