{"mcpServers": {"deepagent-direct": {"command": "C:\\Program Files\\nodejs\\node.exe", "args": ["out/mcp-launcher.js"], "cwd": ".", "description": "Deep Agent MCP Server - Direct Node.js path"}, "deepagent-batch": {"command": ".\\start-mcp-server.bat", "args": [], "cwd": ".", "description": "Deep Agent MCP Server - Using batch file wrapper"}, "deepagent-powershell": {"command": "powershell.exe", "args": ["-ExecutionPolicy", "Bypass", "-File", ".\\start-mcp-server.ps1"], "cwd": ".", "description": "Deep Agent MCP Server - Using PowerShell wrapper"}}}