import { Server } from '@modelcontextprotocol/sdk/server/index.js';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';
import {
  CallToolRequestSchema,
  ListToolsRequestSchema,
  Tool,
} from '@modelcontextprotocol/sdk/types.js';
import * as net from 'net';

interface IPCRequest {
  id: string;
  method: 'plan' | 'execute' | 'planAndExecute';
  params: any;
}

interface IPCResponse {
  id: string;
  result?: any;
  error?: string;
}

class VSCodeIPCClient {
  private socket: net.Socket | null = null;
  private pendingRequests = new Map<string, { resolve: Function; reject: Function }>();
  private requestId = 0;

  async connect(): Promise<void> {
    return new Promise((resolve, reject) => {
      // Try to connect to the VS Code extension's IPC server
      this.socket = net.createConnection({ port: 3002, host: 'localhost' }, () => {
        console.log('🔗 Connected to VS Code extension IPC server');
        resolve();
      });

      this.socket.on('error', (err) => {
        console.log('⚠️ Could not connect to VS Code extension IPC server:', err.message);
        console.log('📝 Falling back to mock implementation');
        this.socket = null;
        resolve(); // Don't reject, just fall back to mock
      });

      this.socket.on('data', (data) => {
        try {
          const response: IPCResponse = JSON.parse(data.toString());
          const pending = this.pendingRequests.get(response.id);
          if (pending) {
            this.pendingRequests.delete(response.id);
            if (response.error) {
              pending.reject(new Error(response.error));
            } else {
              pending.resolve(response.result);
            }
          }
        } catch (error) {
          console.error('❌ Error parsing IPC response:', error);
        }
      });

      this.socket.on('close', () => {
        console.log('🔌 IPC connection closed');
        this.socket = null;
      });
    });
  }

  async sendRequest(method: IPCRequest['method'], params: any): Promise<any> {
    if (!this.socket) {
      throw new Error('Not connected to VS Code extension');
    }

    const id = (++this.requestId).toString();
    const request: IPCRequest = { id, method, params };

    return new Promise((resolve, reject) => {
      this.pendingRequests.set(id, { resolve, reject });

      this.socket!.write(JSON.stringify(request) + '\n');

      // Timeout after 2 minutes for complex planning/execution operations
      setTimeout(() => {
        if (this.pendingRequests.has(id)) {
          this.pendingRequests.delete(id);
          reject(new Error('Request timeout'));
        }
      }, 120000);
    });
  }

  disconnect() {
    if (this.socket) {
      this.socket.end();
      this.socket = null;
    }
  }
}

export class DeepAgentMCPServer {
  private server: Server;
  private transport: StdioServerTransport;
  private ipcClient: VSCodeIPCClient;

  constructor() {
    this.ipcClient = new VSCodeIPCClient();
    this.server = new Server(
      {
        name: 'deepagent-mcp-server',
        version: '1.0.0',
      },
      {
        capabilities: {
          tools: {},
        },
      }
    );

    this.transport = new StdioServerTransport();
    this.setupHandlers();
  }

  private setupHandlers() {
    // List available tools
    this.server.setRequestHandler(ListToolsRequestSchema, async () => {
      return {
        tools: [
          {
            name: 'deepagent_plan',
            description: 'Create a detailed action plan for a complex task using Claude Opus',
            inputSchema: {
              type: 'object',
              properties: {
                task: {
                  type: 'string',
                  description: 'The high-level task to create a plan for',
                },
              },
              required: ['task'],
            },
          },
          {
            name: 'deepagent_execute_step',
            description: 'Execute a single step from a plan using Claude Sonnet with VS Code tools',
            inputSchema: {
              type: 'object',
              properties: {
                step: {
                  type: 'string',
                  description: 'The specific step to execute',
                },
                context: {
                  type: 'string',
                  description: 'Additional context about the overall task (optional)',
                },
              },
              required: ['step'],
            },
          },
          {
            name: 'deepagent_plan_and_execute',
            description: 'Complete workflow: create a plan and execute all steps for a complex task',
            inputSchema: {
              type: 'object',
              properties: {
                task: {
                  type: 'string',
                  description: 'The high-level task to plan and execute',
                },
                max_steps: {
                  type: 'number',
                  description: 'Maximum number of steps to execute (default: 5)',
                  default: 5,
                },
              },
              required: ['task'],
            },
          },
        ] as Tool[],
      };
    });

    // Handle tool calls
    this.server.setRequestHandler(CallToolRequestSchema, async (request) => {
      const { name, arguments: args } = request.params;

      if (!args || typeof args !== 'object') {
        return {
          content: [
            {
              type: 'text',
              text: 'Error: Invalid arguments provided',
            },
          ],
          isError: true,
        };
      }

      try {
        switch (name) {
          case 'deepagent_plan':
            return await this.handlePlan(args.task as string);

          case 'deepagent_execute_step':
            return await this.handleExecuteStep(args.step as string, args.context as string);

          case 'deepagent_plan_and_execute':
            return await this.handlePlanAndExecute(args.task as string, (args.max_steps as number) || 5);

          default:
            throw new Error(`Unknown tool: ${name}`);
        }
      } catch (error) {
        return {
          content: [
            {
              type: 'text',
              text: `Error: ${error instanceof Error ? error.message : String(error)}`,
            },
          ],
          isError: true,
        };
      }
    });
  }

  private async handlePlan(task: string) {
    try {
      console.log('🧠 MCP: Creating plan for task:', task);

      try {
        // Try to use the real VS Code extension via IPC
        const plan = await this.ipcClient.sendRequest('plan', { task });
        const planText = plan.map((step: string, index: number) => `${index + 1}. ${step}`).join('\n');

        return {
          content: [
            {
              type: 'text',
              text: `## Plan for: ${task}\n\n${planText}`,
            },
          ],
        };
      } catch (ipcError) {
        console.log('⚠️ IPC failed, using mock plan:', ipcError instanceof Error ? ipcError.message : String(ipcError));

        // Fallback to mock plan
        const mockPlan = [
          `Analyze the requirements for: ${task}`,
          'Break down the task into smaller components',
          'Implement the core functionality',
          'Add error handling and validation',
          'Write tests and documentation',
          'Review and refine the implementation'
        ];

        const planText = mockPlan.map((step, index) => `${index + 1}. ${step}`).join('\n');

        return {
          content: [
            {
              type: 'text',
              text: `## Plan for: ${task}\n\n${planText}\n\n*Note: Using mock plan. For full functionality, ensure VS Code extension IPC server is running.*`,
            },
          ],
        };
      }
    } catch (error) {
      throw new Error(`Failed to create plan: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  private async handleExecuteStep(step: string, context?: string) {
    try {
      console.log('⚡ MCP: Executing step:', step);

      try {
        // Try to use the real VS Code extension via IPC
        const result = await this.ipcClient.sendRequest('execute', { step, context });

        return {
          content: [
            {
              type: 'text',
              text: `## Executed Step: ${step}\n\n${result}`,
            },
          ],
        };
      } catch (ipcError) {
        console.log('⚠️ IPC failed, using mock execution:', ipcError instanceof Error ? ipcError.message : String(ipcError));

        // Fallback to mock execution result
        const mockResult = `
**Step Analysis:** ${step}

**Approach:**
- Identified the key requirements for this step
- Planned the implementation strategy
- Considered potential challenges and solutions

**Mock Implementation:**
This is a standalone MCP server demonstration. For actual code execution with VS Code tools, ensure the VS Code extension IPC server is running.

**Next Steps:**
- Review the implementation
- Test the functionality
- Make any necessary adjustments

${context ? `**Context:** ${context}` : ''}
        `.trim();

        return {
          content: [
            {
              type: 'text',
              text: `## Executed Step: ${step}\n\n${mockResult}`,
            },
          ],
        };
      }
    } catch (error) {
      throw new Error(`Failed to execute step: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  private async handlePlanAndExecute(task: string, maxSteps: number) {
    try {
      console.log('🚀 MCP: Planning and executing task:', task);

      try {
        // Try to use the real VS Code extension via IPC
        const result = await this.ipcClient.sendRequest('planAndExecute', { task, maxSteps });

        return {
          content: [
            {
              type: 'text',
              text: result,
            },
          ],
        };
      } catch (ipcError) {
        console.log('⚠️ IPC failed, using mock plan and execute:', ipcError instanceof Error ? ipcError.message : String(ipcError));

        // Fallback to mock plan and execution
        const mockPlan = [
          `Analyze the requirements for: ${task}`,
          'Break down the task into smaller components',
          'Implement the core functionality',
          'Add error handling and validation',
          'Write tests and documentation',
          'Review and refine the implementation'
        ];

        let result = `## Plan and Execution for: ${task}\n\n### Plan:\n`;
        result += mockPlan.map((step, index) => `${index + 1}. ${step}`).join('\n');
        result += '\n\n### Execution:\n\n';

        // Mock execution of each step (up to maxSteps)
        const stepsToExecute = mockPlan.slice(0, maxSteps);

        for (let i = 0; i < stepsToExecute.length; i++) {
          const step = stepsToExecute[i];
          result += `#### Step ${i + 1}: ${step}\n`;
          result += `✅ **Completed:** ${step}\n`;
          result += `**Details:** This step has been analyzed and would be implemented using appropriate VS Code tools and Claude Sonnet.\n\n`;
        }

        if (mockPlan.length > maxSteps) {
          result += `\n*Note: Only executed first ${maxSteps} of ${mockPlan.length} steps. Remaining steps can be executed separately.*\n`;
        }

        result += `\n*Note: Using mock implementation. For actual code execution with VS Code tools, ensure the VS Code extension IPC server is running.*\n`;

        return {
          content: [
            {
              type: 'text',
              text: result,
            },
          ],
        };
      }
    } catch (error) {
      throw new Error(`Failed to plan and execute: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  async start() {
    // Try to connect to VS Code extension IPC server
    await this.ipcClient.connect();

    await this.server.connect(this.transport);
    console.log('🌐 Deep Agent MCP Server started');
  }

  async stop() {
    this.ipcClient.disconnect();
    await this.server.close();
    console.log('🛑 Deep Agent MCP Server stopped');
  }
}
