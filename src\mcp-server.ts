import { Server } from '@modelcontextprotocol/sdk/server/index.js';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';
import {
  CallToolRequestSchema,
  ListToolsRequestSchema,
  Tool,
} from '@modelcontextprotocol/sdk/types.js';

export class DeepAgentMCPServer {
  private server: Server;
  private transport: StdioServerTransport;

  constructor() {
    this.server = new Server(
      {
        name: 'deepagent-mcp-server',
        version: '1.0.0',
      },
      {
        capabilities: {
          tools: {},
        },
      }
    );

    this.transport = new StdioServerTransport();
    this.setupHandlers();
  }

  private setupHandlers() {
    // List available tools
    this.server.setRequestHandler(ListToolsRequestSchema, async () => {
      return {
        tools: [
          {
            name: 'deepagent_plan',
            description: 'Create a detailed action plan for a complex task using Claude Opus',
            inputSchema: {
              type: 'object',
              properties: {
                task: {
                  type: 'string',
                  description: 'The high-level task to create a plan for',
                },
              },
              required: ['task'],
            },
          },
          {
            name: 'deepagent_execute_step',
            description: 'Execute a single step from a plan using Claude Sonnet with VS Code tools',
            inputSchema: {
              type: 'object',
              properties: {
                step: {
                  type: 'string',
                  description: 'The specific step to execute',
                },
                context: {
                  type: 'string',
                  description: 'Additional context about the overall task (optional)',
                },
              },
              required: ['step'],
            },
          },
          {
            name: 'deepagent_plan_and_execute',
            description: 'Complete workflow: create a plan and execute all steps for a complex task',
            inputSchema: {
              type: 'object',
              properties: {
                task: {
                  type: 'string',
                  description: 'The high-level task to plan and execute',
                },
                max_steps: {
                  type: 'number',
                  description: 'Maximum number of steps to execute (default: 5)',
                  default: 5,
                },
              },
              required: ['task'],
            },
          },
        ] as Tool[],
      };
    });

    // Handle tool calls
    this.server.setRequestHandler(CallToolRequestSchema, async (request) => {
      const { name, arguments: args } = request.params;

      if (!args || typeof args !== 'object') {
        return {
          content: [
            {
              type: 'text',
              text: 'Error: Invalid arguments provided',
            },
          ],
          isError: true,
        };
      }

      try {
        switch (name) {
          case 'deepagent_plan':
            return await this.handlePlan(args.task as string);

          case 'deepagent_execute_step':
            return await this.handleExecuteStep(args.step as string, args.context as string);

          case 'deepagent_plan_and_execute':
            return await this.handlePlanAndExecute(args.task as string, (args.max_steps as number) || 5);

          default:
            throw new Error(`Unknown tool: ${name}`);
        }
      } catch (error) {
        return {
          content: [
            {
              type: 'text',
              text: `Error: ${error instanceof Error ? error.message : String(error)}`,
            },
          ],
          isError: true,
        };
      }
    });
  }

  private async handlePlan(task: string) {
    try {
      console.log('🧠 MCP: Creating plan for task:', task);

      // For now, return a mock plan since we can't access VS Code APIs directly
      // In a real implementation, this would need to communicate with the VS Code extension
      const mockPlan = [
        `Analyze the requirements for: ${task}`,
        'Break down the task into smaller components',
        'Implement the core functionality',
        'Add error handling and validation',
        'Write tests and documentation',
        'Review and refine the implementation'
      ];

      const planText = mockPlan.map((step, index) => `${index + 1}. ${step}`).join('\n');

      return {
        content: [
          {
            type: 'text',
            text: `## Plan for: ${task}\n\n${planText}\n\n*Note: This is a standalone MCP server. For full functionality, use @deepagent in VS Code Chat.*`,
          },
        ],
      };
    } catch (error) {
      throw new Error(`Failed to create plan: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  private async handleExecuteStep(step: string, context?: string) {
    try {
      console.log('⚡ MCP: Executing step:', step);

      // For now, return a mock execution result
      // In a real implementation, this would need to communicate with the VS Code extension
      const mockResult = `
**Step Analysis:** ${step}

**Approach:**
- Identified the key requirements for this step
- Planned the implementation strategy
- Considered potential challenges and solutions

**Mock Implementation:**
This is a standalone MCP server demonstration. For actual code execution with VS Code tools, use @deepagent in VS Code Chat.

**Next Steps:**
- Review the implementation
- Test the functionality
- Make any necessary adjustments

${context ? `**Context:** ${context}` : ''}
      `.trim();

      return {
        content: [
          {
            type: 'text',
            text: `## Executed Step: ${step}\n\n${mockResult}`,
          },
        ],
      };
    } catch (error) {
      throw new Error(`Failed to execute step: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  private async handlePlanAndExecute(task: string, maxSteps: number) {
    try {
      console.log('🚀 MCP: Planning and executing task:', task);

      // Create a mock plan
      const mockPlan = [
        `Analyze the requirements for: ${task}`,
        'Break down the task into smaller components',
        'Implement the core functionality',
        'Add error handling and validation',
        'Write tests and documentation',
        'Review and refine the implementation'
      ];

      let result = `## Plan and Execution for: ${task}\n\n### Plan:\n`;
      result += mockPlan.map((step, index) => `${index + 1}. ${step}`).join('\n');
      result += '\n\n### Execution:\n\n';

      // Mock execution of each step (up to maxSteps)
      const stepsToExecute = mockPlan.slice(0, maxSteps);

      for (let i = 0; i < stepsToExecute.length; i++) {
        const step = stepsToExecute[i];
        result += `#### Step ${i + 1}: ${step}\n`;
        result += `✅ **Completed:** ${step}\n`;
        result += `**Details:** This step has been analyzed and would be implemented using appropriate VS Code tools and Claude Sonnet.\n\n`;
      }

      if (mockPlan.length > maxSteps) {
        result += `\n*Note: Only executed first ${maxSteps} of ${mockPlan.length} steps. Remaining steps can be executed separately.*\n`;
      }

      result += `\n*Note: This is a standalone MCP server demonstration. For actual code execution with VS Code tools, use @deepagent in VS Code Chat.*\n`;

      return {
        content: [
          {
            type: 'text',
            text: result,
          },
        ],
      };
    } catch (error) {
      throw new Error(`Failed to plan and execute: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  async start() {
    await this.server.connect(this.transport);
    console.log('🌐 Deep Agent MCP Server started');
  }

  async stop() {
    await this.server.close();
    console.log('🛑 Deep Agent MCP Server stopped');
  }
}
