# Session-Isolated Executor Architecture

## 🎯 Overview

This document describes the new **Session-Isolated Executor Architecture** that enables multiple concurrent executors to run simultaneously without context contamination. Each executor maintains its own session context while having full access to VS Code tools for direct file editing.

## 🏗️ Architecture Components

### 1. **SessionIsolatedExecutor** (`src/session-executor.ts`)
- **Purpose**: Individual executor with isolated session context
- **Features**:
  - ✅ Direct access to `vscode.lm.tools` (all VS Code Language Model Tools)
  - ✅ Session-specific context isolation
  - ✅ Progress monitoring and callbacks
  - ✅ Automatic history management
  - ✅ Direct file editing capabilities

### 2. **ConcurrentExecutionManager** (`src/execution-manager.ts`)
- **Purpose**: Coordinates multiple session-isolated executors
- **Features**:
  - ✅ Task queuing and distribution
  - ✅ Concurrent execution management (configurable max concurrency)
  - ✅ Progress monitoring across all sessions
  - ✅ Automatic cleanup of completed sessions
  - ✅ Result aggregation and reporting

### 3. **Enhanced IPC Bridge** (`src/ipc-bridge.ts`)
- **Purpose**: Session-aware communication between MCP server and VS Code extension
- **Features**:
  - ✅ Session creation and management
  - ✅ Session-isolated execution
  - ✅ Context preservation across session calls
  - ✅ Automatic session cleanup

### 4. **SessionMonitor** (`src/session-monitor.ts`)
- **Purpose**: Monitors session lifecycle and provides metrics
- **Features**:
  - ✅ Real-time session metrics
  - ✅ Health monitoring and reporting
  - ✅ Automatic cleanup of inactive sessions
  - ✅ Performance analytics

### 5. **Updated MCP Server** (`src/mcp-server.ts`)
- **Purpose**: Exposes session-isolated execution via MCP protocol
- **New Tools**:
  - `deepagent_create_session` - Create isolated execution session
  - `deepagent_execute_in_session` - Execute step within specific session
  - `deepagent_close_session` - Clean up session
  - `deepagent_list_sessions` - List active sessions

## 🚀 Key Benefits

### ✅ **Direct Tool Access**
- Each executor has direct access to `vscode.lm.tools`
- No intermediate chat sessions required
- Claude Sonnet directly calls `copilot_insertEdit`, `copilot_createFile`, etc.
- Real file editing capabilities

### ✅ **Context Isolation**
- Each session maintains its own context and history
- No interference between concurrent executors
- Session-specific prompts and context management
- Isolated error handling

### ✅ **Concurrent Execution**
- Multiple tasks can run simultaneously
- Configurable concurrency limits
- Automatic task queuing and distribution
- Progress monitoring across all sessions

### ✅ **Robust Session Management**
- Automatic session lifecycle management
- Cleanup of inactive or completed sessions
- Session health monitoring
- Resource management and cleanup

## 📋 Usage Examples

### Basic Session Executor Usage

```typescript
import { SessionIsolatedExecutor } from './session-executor';

const executor = new SessionIsolatedExecutor({
  taskDescription: 'Create TypeScript utility functions',
  onProgress: (progress) => {
    console.log(`Progress: ${progress.type} - ${progress.content}`);
  }
});

// Execute a step with direct tool access
const result = await executor.executeStep(
  'Create a utility function for date formatting',
  'The function should handle multiple date formats'
);

// Clean up
executor.dispose();
```

### Concurrent Execution Manager

```typescript
import { ConcurrentExecutionManager, TaskDefinition } from './execution-manager';

const manager = new ConcurrentExecutionManager({
  maxConcurrentExecutors: 3,
  progressCallback: (taskId, progress) => {
    console.log(`Task ${taskId}: ${progress.type}`);
  }
});

const tasks: TaskDefinition[] = [
  {
    id: 'task-1',
    description: 'Create user interface components',
    steps: ['Create Button component', 'Add TypeScript types', 'Write tests']
  },
  {
    id: 'task-2', 
    description: 'Set up database models',
    steps: ['Create User model', 'Add validation', 'Create migrations']
  }
];

// Execute all tasks concurrently
const results = await manager.executeConcurrentTasks(tasks);
```

### MCP Server Session Tools

```bash
# Create a new session
{
  "tool": "deepagent_create_session",
  "arguments": {
    "task_description": "Implement authentication system"
  }
}

# Execute in session
{
  "tool": "deepagent_execute_in_session", 
  "arguments": {
    "session_id": "session-1234567890-abc123",
    "step": "Create login component with TypeScript",
    "context": "Use React hooks and proper error handling"
  }
}

# Close session
{
  "tool": "deepagent_close_session",
  "arguments": {
    "session_id": "session-1234567890-abc123"
  }
}
```

## 🔧 Configuration

### Extension Settings

```json
{
  "deepagent.mcp.enabled": true,
  "deepagent.sessions.maxConcurrent": 3,
  "deepagent.sessions.cleanupInterval": 60000,
  "deepagent.sessions.maxInactiveTime": 600000
}
```

### Execution Manager Options

```typescript
const manager = new ConcurrentExecutionManager({
  maxConcurrentExecutors: 5,        // Max concurrent sessions
  progressCallback: (taskId, progress) => { /* handle progress */ },
  completionCallback: (result) => { /* handle completion */ }
});
```

### Session Monitor Options

```typescript
const monitor = createSessionMonitor(manager, {
  cleanupInterval: 30000,     // 30 seconds
  maxInactiveTime: 300000,    // 5 minutes
  onSessionCompleted: (result) => { /* handle completion */ },
  onCleanup: (count) => { /* handle cleanup */ }
});
```

## 📊 Monitoring and Metrics

### Session Metrics

```typescript
const metrics = monitor.getMetrics();
console.log({
  totalSessions: metrics.totalSessions,
  activeSessions: metrics.activeSessions,
  completedSessions: metrics.completedSessions,
  failedSessions: metrics.failedSessions,
  averageDuration: metrics.averageDuration
});
```

### Health Monitoring

```typescript
const health = monitor.getHealthReport();
console.log(`System health: ${health.status}`);
health.issues.forEach(issue => console.log(`Issue: ${issue}`));
health.recommendations.forEach(rec => console.log(`Recommendation: ${rec}`));
```

## 🔄 Migration from Previous Architecture

### Before (Single Session)
```typescript
// Old approach - single session, potential context contamination
const result = await ipcClient.sendRequest('execute', { step: 'Create file' });
```

### After (Session Isolated)
```typescript
// New approach - session isolated, no context contamination
const sessionId = await ipcClient.createSession('File creation task');
const result = await ipcClient.executeInSession(sessionId, 'Create file');
await ipcClient.closeSession(sessionId);
```

## 🧪 Testing

### Demo Command
Run the session integration demo:
```
Ctrl+Shift+P -> "Deep Agent: Demonstrate Session Integration"
```

This command demonstrates:
- Single task execution
- Concurrent task execution  
- Task queuing
- Session monitoring
- Direct executor usage

## 🔍 Troubleshooting

### Common Issues

1. **Sessions not cleaning up**
   - Check `cleanupInterval` and `maxInactiveTime` settings
   - Monitor session metrics for inactive sessions
   - Force cleanup with `monitor.forceCleanup()`

2. **High failure rate**
   - Review task complexity and step definitions
   - Check error logs for specific failure patterns
   - Consider breaking down complex tasks

3. **Performance issues**
   - Reduce `maxConcurrentExecutors` if system is overloaded
   - Monitor average execution duration
   - Check for resource contention

### Debug Logging

Enable detailed logging:
```typescript
console.log('Session status:', executor.getSessionInfo());
console.log('Manager status:', manager.getExecutorStatus());
console.log('Monitor metrics:', monitor.getMetrics());
```

## 🎯 Next Steps

1. **Performance Optimization**: Fine-tune concurrency limits and cleanup intervals
2. **Enhanced Monitoring**: Add more detailed metrics and alerting
3. **Session Persistence**: Consider persisting session state across extension restarts
4. **Advanced Scheduling**: Implement priority-based task scheduling
5. **Resource Management**: Add memory and CPU usage monitoring

## 📚 Related Files

- `src/session-executor.ts` - Core session executor implementation
- `src/execution-manager.ts` - Concurrent execution coordination
- `src/session-monitor.ts` - Session lifecycle monitoring
- `src/ipc-bridge.ts` - Enhanced IPC with session support
- `src/mcp-server.ts` - Updated MCP server with session tools
- `src/session-integration-example.ts` - Complete usage examples
