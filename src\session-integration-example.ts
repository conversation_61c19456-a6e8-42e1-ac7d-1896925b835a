import * as vscode from 'vscode';
import { ConcurrentExecutionManager, TaskDefinition } from './execution-manager';
import { SessionMonitor, createSessionMonitor } from './session-monitor';
import { SessionIsolatedExecutor } from './session-executor';

/**
 * Example integration showing how to use the session-isolated architecture
 * This demonstrates the complete workflow from task creation to execution monitoring
 */
export class SessionIntegrationExample {
  private executionManager: ConcurrentExecutionManager;
  private sessionMonitor: SessionMonitor;

  constructor() {
    // Initialize the execution manager with callbacks
    this.executionManager = new ConcurrentExecutionManager({
      maxConcurrentExecutors: 3,
      progressCallback: (taskId, progress) => {
        console.log(`📈 Progress for task ${taskId}:`, progress.type, progress.content?.substring(0, 100));
      },
      completionCallback: (result) => {
        console.log(`✅ Task ${result.taskId} completed:`, result.success ? 'SUCCESS' : 'FAILED');
        if (!result.success && result.error) {
          console.error(`❌ Error: ${result.error}`);
        }
      }
    });

    // Initialize session monitoring
    this.sessionMonitor = createSessionMonitor(this.executionManager, {
      cleanupInterval: 30000, // 30 seconds for demo
      maxInactiveTime: 300000, // 5 minutes for demo
      onSessionCompleted: (result) => {
        console.log(`🎯 Session completed: ${result.sessionId} (${result.duration}ms)`);
      },
      onCleanup: (cleanedCount) => {
        console.log(`🧹 Cleaned up ${cleanedCount} inactive sessions`);
      }
    });
  }

  /**
   * Start the session integration system
   */
  async start(): Promise<void> {
    console.log('🚀 SessionIntegration: Starting session-isolated execution system...');
    
    this.sessionMonitor.start();
    
    console.log('✅ SessionIntegration: System started successfully');
  }

  /**
   * Stop the session integration system
   */
  async stop(): Promise<void> {
    console.log('🛑 SessionIntegration: Stopping session-isolated execution system...');
    
    this.sessionMonitor.stop();
    await this.executionManager.dispose();
    
    console.log('✅ SessionIntegration: System stopped successfully');
  }

  /**
   * Example: Execute a single task with session isolation
   */
  async executeSingleTask(taskDescription: string, steps: string[]): Promise<void> {
    console.log(`🎯 SessionIntegration: Executing single task: ${taskDescription}`);

    const task: TaskDefinition = {
      id: `task-${Date.now()}`,
      description: taskDescription,
      steps: steps
    };

    try {
      const result = await this.executionManager.executeTask(task);
      console.log(`✅ Single task completed:`, result);
    } catch (error) {
      console.error(`❌ Single task failed:`, error);
    }
  }

  /**
   * Example: Execute multiple concurrent tasks
   */
  async executeConcurrentTasks(): Promise<void> {
    console.log('🎯 SessionIntegration: Executing multiple concurrent tasks...');

    const tasks: TaskDefinition[] = [
      {
        id: 'task-file-creation',
        description: 'Create multiple TypeScript files',
        steps: [
          'Create a new TypeScript interface file called User.ts',
          'Add properties: id, name, email, createdAt',
          'Export the interface'
        ]
      },
      {
        id: 'task-documentation',
        description: 'Update project documentation',
        steps: [
          'Create a README.md file',
          'Add project description and setup instructions',
          'Include usage examples'
        ]
      },
      {
        id: 'task-configuration',
        description: 'Set up project configuration',
        steps: [
          'Create tsconfig.json with strict settings',
          'Add package.json scripts for build and test',
          'Configure ESLint rules'
        ]
      }
    ];

    try {
      const results = await this.executionManager.executeConcurrentTasks(tasks);
      console.log(`✅ Concurrent tasks completed:`, results.length, 'tasks');
      
      results.forEach(result => {
        console.log(`  - ${result.taskId}: ${result.success ? 'SUCCESS' : 'FAILED'}`);
      });
    } catch (error) {
      console.error(`❌ Concurrent tasks failed:`, error);
    }
  }

  /**
   * Example: Queue tasks for sequential execution
   */
  async queueTasks(): Promise<void> {
    console.log('🎯 SessionIntegration: Queuing tasks for execution...');

    const tasks: TaskDefinition[] = [
      {
        id: 'setup-project',
        description: 'Set up new project structure',
        steps: [
          'Create src directory',
          'Create tests directory',
          'Create docs directory'
        ]
      },
      {
        id: 'implement-core',
        description: 'Implement core functionality',
        steps: [
          'Create main application class',
          'Implement core business logic',
          'Add error handling'
        ]
      }
    ];

    for (const task of tasks) {
      await this.executionManager.queueTask(task);
      console.log(`📋 Queued task: ${task.id}`);
    }
  }

  /**
   * Example: Monitor system health and metrics
   */
  async monitorSystem(): Promise<void> {
    console.log('📊 SessionIntegration: Monitoring system health...');

    // Get current metrics
    const metrics = this.sessionMonitor.getMetrics();
    console.log('📈 Current metrics:', metrics);

    // Get detailed status
    const status = this.sessionMonitor.getDetailedStatus();
    console.log('📋 Active sessions:', status.activeSessions.length);
    console.log('📊 Recent results:', status.recentResults.length);

    // Get health report
    const health = this.sessionMonitor.getHealthReport();
    console.log(`🏥 System health: ${health.status}`);
    
    if (health.issues.length > 0) {
      console.log('⚠️ Issues found:');
      health.issues.forEach(issue => console.log(`  - ${issue}`));
      
      console.log('💡 Recommendations:');
      health.recommendations.forEach(rec => console.log(`  - ${rec}`));
    }
  }

  /**
   * Example: Direct session executor usage
   */
  async useDirectSessionExecutor(): Promise<void> {
    console.log('🎯 SessionIntegration: Using direct session executor...');

    const executor = new SessionIsolatedExecutor({
      taskDescription: 'Create a simple utility function',
      onProgress: (progress) => {
        console.log(`📈 Direct executor progress:`, progress.type, progress.content?.substring(0, 50));
      }
    });

    try {
      const result = await executor.executeStep(
        'Create a TypeScript utility function that formats dates',
        'The function should accept a Date object and return a formatted string'
      );

      console.log(`✅ Direct execution result:`, result.substring(0, 200) + '...');
      
      // Get session info
      const sessionInfo = executor.getSessionInfo();
      console.log(`📊 Session info:`, sessionInfo);

    } catch (error) {
      console.error(`❌ Direct execution failed:`, error);
    } finally {
      // Clean up
      executor.dispose();
    }
  }

  /**
   * Get current system status
   */
  getSystemStatus(): {
    executorStatus: any[];
    metrics: any;
    health: any;
  } {
    return {
      executorStatus: this.executionManager.getExecutorStatus(),
      metrics: this.sessionMonitor.getMetrics(),
      health: this.sessionMonitor.getHealthReport()
    };
  }
}

/**
 * VS Code command to demonstrate the session integration
 */
export async function demonstrateSessionIntegration(): Promise<void> {
  const integration = new SessionIntegrationExample();

  try {
    // Start the system
    await integration.start();

    // Run various examples
    await integration.executeSingleTask(
      'Create a simple TypeScript class',
      [
        'Create a new file called Calculator.ts',
        'Implement basic arithmetic operations',
        'Add proper TypeScript types and documentation'
      ]
    );

    await integration.queueTasks();
    await integration.monitorSystem();
    await integration.useDirectSessionExecutor();

    // Show final status
    const status = integration.getSystemStatus();
    console.log('🎯 Final system status:', status);

  } catch (error) {
    console.error('❌ Session integration demo failed:', error);
  } finally {
    // Clean up
    await integration.stop();
  }
}

/**
 * Register VS Code command for the demo
 */
export function registerSessionIntegrationCommands(context: vscode.ExtensionContext): void {
  const disposable = vscode.commands.registerCommand(
    'deepagent.demonstrateSessionIntegration',
    demonstrateSessionIntegration
  );

  context.subscriptions.push(disposable);
  console.log('✅ Registered session integration demo command');
}
