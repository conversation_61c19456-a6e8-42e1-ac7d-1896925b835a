#!/usr/bin/env node

/**
 * Test script to verify the timeout fix for MCP server
 */

const { spawn } = require('child_process');
const path = require('path');

async function testTimeoutFix() {
  console.log('🧪 Testing MCP Server Timeout Fix');
  console.log('=====================================\n');
  
  const serverPath = path.join(__dirname, 'out', 'mcp-launcher.js');
  
  console.log('🚀 Starting MCP Server...');
  
  // Start the MCP server
  const server = spawn('node', [serverPath], {
    stdio: ['pipe', 'pipe', 'pipe'],
    cwd: __dirname
  });
  
  let output = '';
  let errorOutput = '';
  
  server.stdout.on('data', (data) => {
    output += data.toString();
    console.log('📤 Server output:', data.toString().trim());
  });
  
  server.stderr.on('data', (data) => {
    errorOutput += data.toString();
    console.log('📢 Server stderr:', data.toString().trim());
  });
  
  // Give the server a moment to start
  await new Promise(resolve => setTimeout(resolve, 3000));
  
  console.log('\n🧠 Testing plan tool with longer timeout...\n');
  
  // Test the plan tool with a complex task that might take longer
  const planRequest = {
    jsonrpc: '2.0',
    id: 1,
    method: 'tools/call',
    params: {
      name: 'deepagent_plan',
      arguments: {
        task: 'Create a comprehensive TypeScript application with authentication, database integration, and REST API endpoints'
      }
    }
  };
  
  console.log('📝 Sending complex planning request...');
  server.stdin.write(JSON.stringify(planRequest) + '\n');
  
  // Wait longer to see if the timeout fix works
  console.log('⏱️  Waiting up to 3 minutes for response...');
  await new Promise(resolve => setTimeout(resolve, 180000)); // 3 minutes
  
  // Clean shutdown
  server.kill('SIGTERM');
  
  console.log('\n=====================================');
  console.log('📊 Test Results:');
  console.log('=====================================\n');
  
  if (errorOutput.includes('Connected to VS Code extension IPC server')) {
    console.log('✅ IPC Connection: Successfully connected to VS Code extension');
  } else {
    console.log('⚠️  IPC Connection: Using standalone mode (VS Code extension not running)');
  }
  
  if (errorOutput.includes('Request timeout')) {
    console.log('❌ Timeout: Still experiencing timeout issues');
    console.log('💡 Suggestion: The VS Code extension might be taking longer than expected');
  } else if (output.includes('Plan for:')) {
    console.log('✅ Planning: Successfully completed without timeout');
  } else {
    console.log('⚠️  Planning: No clear success or timeout detected');
  }
  
  if (errorOutput.includes('IPC: Creating plan for task')) {
    console.log('✅ IPC Bridge: Successfully receiving requests');
  }
  
  if (errorOutput.includes('IPC: Got Claude Opus model')) {
    console.log('✅ Claude Model: Successfully accessing Claude Opus');
  } else if (errorOutput.includes('IPC: Getting Claude Opus model')) {
    console.log('⚠️  Claude Model: Attempting to get model but may be slow');
  }
  
  console.log('\n🎯 Summary:');
  console.log('- Timeout increased from 30s to 2 minutes');
  console.log('- Added detailed logging for debugging');
  console.log('- IPC bridge has better error reporting');
  
  console.log('\n💡 Next Steps:');
  console.log('1. If still timing out, check VS Code extension is running');
  console.log('2. Verify Claude models are properly configured');
  console.log('3. Check VS Code Output panel for detailed logs');
}

testTimeoutFix().catch(console.error);
