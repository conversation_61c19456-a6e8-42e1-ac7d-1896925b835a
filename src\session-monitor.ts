import { ConcurrentExecutionManager, ExecutionResult } from './execution-manager';
import { ExecutionProgress } from './session-executor';

export interface SessionMetrics {
  totalSessions: number;
  activeSessions: number;
  completedSessions: number;
  failedSessions: number;
  averageDuration: number;
  totalExecutionTime: number;
}

export interface SessionMonitorOptions {
  cleanupInterval?: number; // milliseconds
  maxInactiveTime?: number; // milliseconds
  onSessionCompleted?: (result: ExecutionResult) => void;
  onSessionProgress?: (taskId: string, progress: ExecutionProgress) => void;
  onCleanup?: (cleanedSessions: number) => void;
}

/**
 * Monitors session lifecycle, provides metrics, and handles automatic cleanup
 */
export class SessionMonitor {
  private executionManager: ConcurrentExecutionManager;
  private cleanupInterval: number;
  private maxInactiveTime: number;
  private cleanupTimer: NodeJS.Timeout | null = null;
  private sessionMetrics: SessionMetrics = {
    totalSessions: 0,
    activeSessions: 0,
    completedSessions: 0,
    failedSessions: 0,
    averageDuration: 0,
    totalExecutionTime: 0
  };
  private onSessionCompleted?: (result: ExecutionResult) => void;
  private onSessionProgress?: (taskId: string, progress: ExecutionProgress) => void;
  private onCleanup?: (cleanedSessions: number) => void;

  constructor(executionManager: ConcurrentExecutionManager, options: SessionMonitorOptions = {}) {
    this.executionManager = executionManager;
    this.cleanupInterval = options.cleanupInterval || 60000; // 1 minute
    this.maxInactiveTime = options.maxInactiveTime || 600000; // 10 minutes
    this.onSessionCompleted = options.onSessionCompleted;
    this.onSessionProgress = options.onSessionProgress;
    this.onCleanup = options.onCleanup;

    console.log(`📊 SessionMonitor: Initialized with cleanup interval ${this.cleanupInterval}ms`);
  }

  /**
   * Start monitoring sessions
   */
  start(): void {
    console.log('📊 SessionMonitor: Starting session monitoring...');

    // Set up the execution manager callbacks
    this.setupExecutionManagerCallbacks();

    // Start periodic cleanup
    this.startPeriodicCleanup();

    console.log('✅ SessionMonitor: Session monitoring started');
  }

  /**
   * Stop monitoring sessions
   */
  stop(): void {
    console.log('📊 SessionMonitor: Stopping session monitoring...');

    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
      this.cleanupTimer = null;
    }

    console.log('✅ SessionMonitor: Session monitoring stopped');
  }

  /**
   * Get current session metrics
   */
  getMetrics(): SessionMetrics {
    // Update active sessions count
    const activeExecutors = this.executionManager.getExecutorStatus();
    this.sessionMetrics.activeSessions = activeExecutors.length;

    return { ...this.sessionMetrics };
  }

  /**
   * Get detailed session status
   */
  getDetailedStatus(): {
    metrics: SessionMetrics;
    activeSessions: Array<{
      taskId: string;
      sessionId: string;
      sessionInfo: any;
    }>;
    recentResults: ExecutionResult[];
  } {
    const metrics = this.getMetrics();
    const activeSessions = this.executionManager.getExecutorStatus();
    const recentResults = this.executionManager.getExecutionResults().slice(-10); // Last 10 results

    return {
      metrics,
      activeSessions,
      recentResults
    };
  }

  /**
   * Force cleanup of inactive sessions
   */
  async forceCleanup(): Promise<number> {
    console.log('🧹 SessionMonitor: Forcing cleanup of inactive sessions...');

    const beforeCount = this.sessionMetrics.activeSessions;
    await this.executionManager.cleanup();
    const afterCount = this.executionManager.getExecutorStatus().length;

    const cleanedCount = beforeCount - afterCount;
    
    if (cleanedCount > 0) {
      console.log(`🗑️ SessionMonitor: Cleaned up ${cleanedCount} inactive sessions`);
      
      if (this.onCleanup) {
        this.onCleanup(cleanedCount);
      }
    }

    return cleanedCount;
  }

  /**
   * Get session health report
   */
  getHealthReport(): {
    status: 'healthy' | 'warning' | 'critical';
    issues: string[];
    recommendations: string[];
  } {
    const metrics = this.getMetrics();
    const issues: string[] = [];
    const recommendations: string[] = [];

    // Check for too many active sessions
    if (metrics.activeSessions > 5) {
      issues.push(`High number of active sessions: ${metrics.activeSessions}`);
      recommendations.push('Consider reducing concurrent task load or increasing cleanup frequency');
    }

    // Check failure rate
    const totalCompleted = metrics.completedSessions + metrics.failedSessions;
    if (totalCompleted > 0) {
      const failureRate = metrics.failedSessions / totalCompleted;
      if (failureRate > 0.3) {
        issues.push(`High failure rate: ${(failureRate * 100).toFixed(1)}%`);
        recommendations.push('Review task complexity and error handling');
      }
    }

    // Check average duration
    if (metrics.averageDuration > 300000) { // 5 minutes
      issues.push(`Long average execution time: ${(metrics.averageDuration / 1000).toFixed(1)}s`);
      recommendations.push('Consider breaking down complex tasks into smaller steps');
    }

    let status: 'healthy' | 'warning' | 'critical' = 'healthy';
    if (issues.length > 0) {
      status = issues.length > 2 ? 'critical' : 'warning';
    }

    return {
      status,
      issues,
      recommendations
    };
  }

  private setupExecutionManagerCallbacks(): void {
    // Create a new execution manager with our callbacks
    const originalManager = this.executionManager;
    
    // We'll need to wrap the execution manager to intercept callbacks
    // For now, we'll track metrics manually through periodic updates
    console.log('📊 SessionMonitor: Set up execution manager monitoring');
  }

  private startPeriodicCleanup(): void {
    this.cleanupTimer = setInterval(async () => {
      try {
        await this.performPeriodicMaintenance();
      } catch (error) {
        console.error('❌ SessionMonitor: Error during periodic maintenance:', error);
      }
    }, this.cleanupInterval);

    console.log(`⏰ SessionMonitor: Started periodic cleanup every ${this.cleanupInterval}ms`);
  }

  private async performPeriodicMaintenance(): Promise<void> {
    console.log('🔧 SessionMonitor: Performing periodic maintenance...');

    // Update metrics
    this.updateMetrics();

    // Cleanup inactive sessions
    const cleanedCount = await this.forceCleanup();

    // Log health status
    const health = this.getHealthReport();
    if (health.status !== 'healthy') {
      console.log(`⚠️ SessionMonitor: Health status: ${health.status}`);
      health.issues.forEach(issue => console.log(`  - ${issue}`));
    }

    console.log(`✅ SessionMonitor: Maintenance completed (cleaned ${cleanedCount} sessions)`);
  }

  private updateMetrics(): void {
    const activeExecutors = this.executionManager.getExecutorStatus();
    const results = this.executionManager.getExecutionResults();

    // Update basic counts
    this.sessionMetrics.activeSessions = activeExecutors.length;
    this.sessionMetrics.totalSessions = results.length + activeExecutors.length;
    this.sessionMetrics.completedSessions = results.filter(r => r.success).length;
    this.sessionMetrics.failedSessions = results.filter(r => !r.success).length;

    // Calculate average duration
    const completedResults = results.filter(r => r.duration > 0);
    if (completedResults.length > 0) {
      this.sessionMetrics.totalExecutionTime = completedResults.reduce((sum, r) => sum + r.duration, 0);
      this.sessionMetrics.averageDuration = this.sessionMetrics.totalExecutionTime / completedResults.length;
    }
  }
}

/**
 * Create a session monitor with default settings
 */
export function createSessionMonitor(
  executionManager: ConcurrentExecutionManager,
  options?: SessionMonitorOptions
): SessionMonitor {
  return new SessionMonitor(executionManager, {
    cleanupInterval: 60000, // 1 minute
    maxInactiveTime: 600000, // 10 minutes
    ...options
  });
}
