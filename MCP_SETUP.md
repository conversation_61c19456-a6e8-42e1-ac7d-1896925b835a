# Deep Agent MCP Server Setup

This extension now supports both **Chat Participant mode** (`@deepagent` in Ask mode) and **MCP Server mode** (for Agent/Edit modes).

## What is MCP?

Model Context Protocol (MCP) allows AI models to access external tools and data sources. By running Deep Agent as an MCP server, you can use its planning and execution capabilities in VS Code's Agent mode and Edit mode.

## Architecture

The MCP server uses an IPC (Inter-Process Communication) bridge to access VS Code APIs:

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   MCP Client    │◄──►│   MCP Server     │◄──►│   VS Code       │
│   (Agent mode)  │    │   (Node.js)      │    │   Extension     │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                │                       │
                                │       IPC Bridge      │
                                │    (port 3002)        │
                                └───────────────────────┘
```

**How It Works:**
1. **With VS Code Extension Running**: MCP server connects to the extension via IPC bridge and uses real VS Code APIs (Claude models, tools, etc.)
2. **Standalone Mode**: MCP server falls back to mock implementations for demonstration purposes

## MCP Tools Available

The MCP server provides three tools:

- `deepagent_plan` - Create detailed action plans using Claude Opus
- `deepagent_execute_step` - Execute individual steps using Claude Sonnet with VS Code tools
- `deepagent_plan_and_execute` - Complete workflow automation (plan + execute)

## Quick Setup

### 1. Build the Extension
```bash
npm install
npm run compile
```

### 2. Configure MCP in VS Code

Add the Deep Agent MCP server to your VS Code MCP configuration:

**Option A: Using VS Code Settings UI**
1. Open VS Code Settings (Ctrl+,)
2. Search for "mcp" or "Model Context Protocol"
3. Find the MCP Servers configuration section
4. Add a new server with these details:
   - **Name**: `deepagent`
   - **Command**: `node`
   - **Args**: `["out/mcp-launcher.js"]`
   - **Working Directory**: Path to your deep-agent extension folder

**Option B: Manual Configuration File**
Create or edit your MCP configuration file (usually in your VS Code settings) and add:

```json
{
  "mcp": {
    "servers": {
      "deepagent": {
        "command": "node",
        "args": ["out/mcp-launcher.js"],
        "cwd": "C:\\github\\deep-agent\\deep-agent",
        "description": "Deep Agent MCP Server - Planning and execution tools"
      }
    }
  }
}
```

**Option C: Using the provided config file**
Copy the `mcp-config.json` file from this extension and reference it in your VS Code MCP settings.

### 3. Test the MCP Server

You can test the MCP server directly:

```bash
# From the extension directory
node out/mcp-launcher.js
```

## Available MCP Tools

When using Deep Agent via MCP in Agent mode, you get access to these tools:

### `deepagent_plan`
Creates a detailed action plan for complex tasks using Claude Opus.

**Parameters:**
- `task` (string): The high-level task to create a plan for

**Example:**
```
Use the deepagent_plan tool to create a plan for "Add user authentication to my React app"
```

### `deepagent_execute_step`
Executes a single step from a plan using Claude Sonnet with VS Code tools.

**Parameters:**
- `step` (string): The specific step to execute
- `context` (string, optional): Additional context about the overall task

**Example:**
```
Use deepagent_execute_step to execute "Create login component with email and password fields"
```

### `deepagent_plan_and_execute`
Complete workflow that creates a plan and executes all steps.

**Parameters:**
- `task` (string): The high-level task to plan and execute
- `max_steps` (number, optional): Maximum number of steps to execute (default: 5)

**Example:**
```
Use deepagent_plan_and_execute to "Refactor the authentication system to use OAuth"
```

## Usage Modes

### Ask Mode (Chat Participant)
- Use `@deepagent` in the Chat window
- Works in Ask mode only
- Interactive conversation style
- Full streaming response

### Agent/Edit Mode (MCP Tools)
- Use the MCP tools in Agent mode or Edit mode
- Tools are available in the tools picker
- Can be combined with other MCP tools
- Autonomous execution

## Troubleshooting

### MCP Server Not Starting
1. Ensure the extension is compiled: `npm run compile`
2. Check that Node.js is available in your PATH
3. Verify the MCP configuration paths are correct

### Tools Not Appearing in Agent Mode
1. Check that MCP is enabled in VS Code settings
2. Restart VS Code after adding the MCP configuration
3. Ensure the MCP server is properly configured

### Permission Issues
- The MCP server needs access to VS Code's language model APIs
- Ensure you have proper Copilot access configured

## Configuration

You can configure the MCP server behavior in VS Code settings:

- `deepagent.mcp.enabled`: Enable/disable MCP server (default: true)
- `deepagent.mcp.port`: Port for the MCP server (default: 3001)

## Best Practices

1. **Use Chat Participant for Interactive Work**: Use `@deepagent` when you want to have a conversation and see the planning process.

2. **Use MCP Tools for Autonomous Work**: Use the MCP tools in Agent mode when you want VS Code to autonomously plan and execute complex tasks.

3. **Combine with Other Tools**: MCP tools can be used alongside other MCP servers and VS Code built-in tools.

4. **Start Small**: Begin with simple tasks to understand how the tools work before attempting complex multi-step operations.
