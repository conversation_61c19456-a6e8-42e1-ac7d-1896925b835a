# Multi-Agent Orchestrator - Debug Guide

## How to Debug

1. **Open VS Code Developer Tools:**
   - Press `F12` or `Ctrl+Shift+I` in the Extension Development Host window
   - Go to the "Console" tab

2. **Test the Extension:**
   - In the Extension Development Host, open Copilot Chat (`Ctrl+Alt+I`)
   - Type: `@deepagent hello world`
   - Watch the console for detailed logs

## What to Look For

### Expected Console Output:
```
🚀 Multi-Agent Orchestrator: Extension activating...
✅ Multi-Agent Orchestrator: Chat participant registered as @deepagent
🎯 Handler called with prompt: hello world
🔍 Requesting model: claude-4-opus
📡 Selecting chat models with vendor: copilot, family: claude-4
📋 Available models: [list of models]
```

### Common Issues:

1. **No logs appear:** Extension not loading properly
2. **"Hand<PERSON> called" doesn't appear:** @deepagent not being triggered
3. **Model not found:** Claude 4 models not available (need to use Claude 3)
4. **Empty available models:** Language model provider not configured

## Quick Fixes

### If Claude 4 models aren't available:
The extension is currently trying to use `claude-4-opus` and `claude-4-sonnet`. 
These might need to be changed to actual available model names like:
- `claude-3-opus` 
- `claude-3.5-sonnet`

### To see available models:
Check the console output for "Available models" to see what's actually available.
