#!/usr/bin/env node

/**
 * Simple test script to verify the MCP server can start and respond to basic requests
 */

const { spawn } = require('child_process');
const path = require('path');

async function testMCPServer() {
  console.log('🧪 Testing Deep Agent MCP Server...');
  
  const serverPath = path.join(__dirname, 'out', 'mcp-launcher.js');
  
  // Start the MCP server
  const server = spawn('node', [serverPath], {
    stdio: ['pipe', 'pipe', 'pipe'],
    cwd: __dirname
  });
  
  let output = '';
  let errorOutput = '';
  
  server.stdout.on('data', (data) => {
    output += data.toString();
  });
  
  server.stderr.on('data', (data) => {
    errorOutput += data.toString();
  });
  
  // Give the server a moment to start
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  // Send a list tools request
  const listToolsRequest = {
    jsonrpc: '2.0',
    id: 1,
    method: 'tools/list'
  };
  
  server.stdin.write(JSON.stringify(listToolsRequest) + '\n');
  
  // Wait for response
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  // Clean shutdown
  server.kill('SIGTERM');
  
  console.log('📤 Server stderr output:', errorOutput);
  console.log('📥 Server stdout output:', output);
  
  if (errorOutput.includes('Deep Agent MCP Server is running')) {
    console.log('✅ MCP Server started successfully!');
  } else {
    console.log('❌ MCP Server may have issues starting');
  }
  
  console.log('🏁 Test completed');
}

testMCPServer().catch(console.error);
