#!/usr/bin/env node

/**
 * Simple test script to verify the MCP server can start and respond to basic requests
 */

const { spawn } = require('child_process');
const path = require('path');

async function testMCPServer() {
  console.log('🧪 Testing Deep Agent MCP Server...');

  const serverPath = path.join(__dirname, 'out', 'mcp-launcher.js');

  // Start the MCP server
  const server = spawn('node', [serverPath], {
    stdio: ['pipe', 'pipe', 'pipe'],
    cwd: __dirname
  });

  let output = '';
  let errorOutput = '';

  server.stdout.on('data', (data) => {
    output += data.toString();
  });

  server.stderr.on('data', (data) => {
    errorOutput += data.toString();
  });

  // Give the server a moment to start
  await new Promise(resolve => setTimeout(resolve, 2000));

  console.log('📋 Testing tools list...');

  // Send a list tools request
  const listToolsRequest = {
    jsonrpc: '2.0',
    id: 1,
    method: 'tools/list'
  };

  server.stdin.write(JSON.stringify(listToolsRequest) + '\n');

  // Wait for response
  await new Promise(resolve => setTimeout(resolve, 1000));

  console.log('📋 Testing plan tool...');

  // Test the plan tool
  const planRequest = {
    jsonrpc: '2.0',
    id: 2,
    method: 'tools/call',
    params: {
      name: 'deepagent_plan',
      arguments: {
        task: 'Create a simple Hello World function'
      }
    }
  };

  server.stdin.write(JSON.stringify(planRequest) + '\n');

  // Wait for response
  await new Promise(resolve => setTimeout(resolve, 2000));

  // Clean shutdown
  server.kill('SIGTERM');

  console.log('📤 Server stderr output:', errorOutput);
  console.log('📥 Server stdout output:', output);

  if (errorOutput.includes('Deep Agent MCP Server is running')) {
    console.log('✅ MCP Server started successfully!');
  } else {
    console.log('❌ MCP Server may have issues starting');
  }

  if (output.includes('deepagent_plan')) {
    console.log('✅ MCP Tools are properly registered!');
  } else {
    console.log('❌ MCP Tools may not be registered correctly');
  }

  if (errorOutput.includes('Could not connect to VS Code extension IPC server')) {
    console.log('⚠️ IPC bridge not available - using mock implementations');
  } else if (errorOutput.includes('Connected to VS Code extension IPC server')) {
    console.log('✅ IPC bridge connected - using real VS Code APIs!');
  }

  console.log('🏁 Test completed');
}

testMCPServer().catch(console.error);
