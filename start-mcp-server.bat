@echo off
REM Deep Agent MCP Server Launcher
REM This batch file helps resolve Node.js path issues

echo Starting Deep Agent MCP Server...

REM Try different Node.js paths
if exist "C:\Program Files\nodejs\node.exe" (
    echo Using Node.js from Program Files
    "C:\Program Files\nodejs\node.exe" out\mcp-launcher.js
) else if exist "C:\Program Files (x86)\nodejs\node.exe" (
    echo Using Node.js from Program Files (x86)
    "C:\Program Files (x86)\nodejs\node.exe" out\mcp-launcher.js
) else (
    echo Using Node.js from PATH
    node out\mcp-launcher.js
)
