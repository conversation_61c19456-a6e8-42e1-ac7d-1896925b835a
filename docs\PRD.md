## 0 · Quick-start snapshot (for agent **TL;DR**)

| Item                        | Value                                                                                                                          | Tool failure (regex no match)                   | Tool throws → <PERSON><PERSON> sees error, retries or informs user.                        | Confirm error text is streamed.                                 |
| Model unavailable                               | Graceful error: "<PERSON>-4-<PERSON> not configured. Configure Copilot or BYOM model." | Use try/catch around `getModel`.                                | Model unavailable                               | Graceful error: "Claude-4-<PERSON> not configured. Configure Copilot or BYOM model." | Use try/catch around `getModel`.                                | Model unavailable                               | Graceful error: "Claude-4-<PERSON> not configured. Configure Copilot or BYOM model." | Use try/catch around `getModel`.                                |                                                                                                                                                                                                                                                                                                                                                |
| --------------------------- | -------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **Extension ID**            | `multi-agent.orchestrator`                                                                                                                                                                                                                                                                                                                                                                                                                                                       |
| **Main participant handle** | `@deepagent`                                                                                                                                                                                                                                                                                                                                                                                                                                                                          |
| **Models**                  | Orchestrator ➜ `claude-4-opus`   ·   Sub-agent(s) ➜ `claude-4-sonnet`                                                                                                                                                                                                                                                                                                                                                                                                            |
| **Key helper library**      | `@vscode/chat-extension-utils` (`sendChatParticipantRequest`) ([github.com](https://github.com/microsoft/vscode-chat-extension-utils?utm_source=chatgpt.com))                                                                                                                                                                                                                                                                                                                    |
| **Core APIs used**          | Chat Extension API (`createChatParticipant`) ([code.visualstudio.com](https://code.visualstudio.com/api/extension-guides/chat?utm_source=chatgpt.com))  ·  Language Model API (`selectChatModels`) ([code.visualstudio.com](https://code.visualstudio.com/api/extension-guides/language-model?utm_source=chatgpt.com))  ·  Language Model Tool API (agent-mode tools) ([code.visualstudio.com](https://code.visualstudio.com/api/extension-guides/tools?utm_source=chatgpt.com)) |
| **Execution loop**          | `User → Opus-plan → for step ∈ plan { Sonnet (agent-mode, tools) } → stream result`                                                                                                                                                                                                                                                                                                                                                                                              |

---

## 1 · Objectives & Scope

* Deliver a **TypeScript VS Code Chat extension** that runs fully locally, exposes one chat participant **@deepagent**, and implements a two-phase *Plan-and-Execute* workflow.
* **Phase 1 (Plan):** use Claude 4 Opus via VS Code Language Model API to decompose the user prompt into numbered steps.
* **Phase 2 (Execute):** iterate through each step, invoking Claude 4 Sonnet in **agent mode** with a curated toolset (code search, file read/write, terminal, etc.).
* All streaming is surfaced through @deepagent so the user experiences a single, coherent conversation.
* Safety: any file writes or shell commands rely on VS Code’s built-in confirmation UI for Language Model Tools.

---

## 2 · High-level architecture

```
┌─────────────┐   Chat    ┌──────────────┐  Opus   ┌──────────────┐
│  End User   │ ───────▶ │  @deepagent (TS)  │ ───▶   │  Opus Model   │
└─────────────┘           │  Orchestrator│        │ (plan agent) │
          ▲               └──┬───────────┘        └────┬──────────┘
          │ stream             │ plan[]                    ▲
          │                    ▼ each step                 │ agent-mode
┌──────────────────────────────┴───────────────────────────┼─────────────┐
│   chatUtils.sendChatParticipantRequest(...)              │  Sonnet     │
│   (prompt, tools, model = Sonnet)                        │  model      │
│   ↳ may call Language Model Tools                        │ (executor)  │
│   ↳ streams diff / output back to @deepagent                  │             │
└───────────────────────────────────────────────────────────┴─────────────┘
```

---

## 3 · Project skeleton & file map

```
multi-agent-orchestrator/
├─ package.json            ← contributes chat participant & LM tools
├─ tsconfig.json
├─ src/
│  ├─ extension.ts         ← entry point, activates @deepagent
│  ├─ models.ts            ← helper: fetchModel('claude-4-opus' | 'claude-4-sonnet')
│  ├─ planner.ts           ← build prompt, call Opus, parse numbered plan
│  ├─ executor.ts          ← run one step with Sonnet via agent-mode
│  ├─ tools/
│  │   ├─ searchTool.ts    ← workspace code search
│  │   ├─ editTool.ts      ← file write & diff generation
│  │   └─ testTool.ts      ← run tests in terminal
│  └─ utils/
│      └─ streamHelpers.ts ← pipe inner stream → outer ChatResponseStream
└─ README.md
```

> **Tip** Generate the initial scaffold with `yo code` → **“New Chat Extension (TypeScript)”**.

---

## 4 · Detailed build steps

### 4.1 · Setup

1. `npm init @vscode / yo code` → pick **Chat Extension** template.
2. Add dependencies:

   ```bash
   npm i @vscode/chat-extension-utils@latest
   npm i -D @types/vscode
   ```
3. Enable API proposals in `package.json`:

   ```jsonc
   "enabledApiProposals": ["chat", "lm", "tools"]
   ```

### 4.2 · Manifest (`package.json`)

```jsonc
"contributes": {
  "chatParticipants": [
    {
      "id": "multiAgent.deepagent",
      "name": "deepagent",
      "fullName": "Multi-Agent Orchestrator",
      "description": "Plans with Opus and executes with Sonnet."
    }
  ],
  "languageModelTools": [
    {
      "name": "workspaceSearch",
      "toolReferenceName": "workspace_search",
      "displayName": "Workspace Search",
      "description": "Find code snippets matching a regex.",
      "inputSchema": {
        "type": "object",
        "properties": { "query": { "type": "string" } },
        "required": ["query"]
      },
      "tags": ["code", "safe"]
    }
    // …add editTool, testTool similarly
  ]
}
```

### 4.3 · Model helper (`models.ts`)

```ts
import * as vscode from 'vscode';
export async function getModel(id: string) {
  const models = await vscode.lm.selectChatModels({ vendor: 'copilot', family: 'claude-4' });
  const m = models.find(x => x.id === id);
  if (!m) { throw new Error(`Model ${id} unavailable`); }
  return m;
}
```

### 4.4 · Planner (`planner.ts`)

```ts
export async function createPlan(model: vscode.LanguageModelChat, userPrompt: string, token: vscode.CancellationToken) {
  const resp = await model.sendRequest(
    [
      vscode.LanguageModelChatMessage.System("You are a senior software architect. Produce an ordered action plan (1.,2.,3.) and nothing else."),
      vscode.LanguageModelChatMessage.User(userPrompt)
    ],
    {},
    token
  );
  const text = await resp.text.join('');
  return text.split(/\n\d+\./).filter(Boolean).map(s => s.trim());
}
```

### 4.5 · Executor (`executor.ts`)

```ts
import * as chatUtils from '@vscode/chat-extension-utils';
export async function runStep(
  step: string,
  chatCtx: vscode.ChatContext,
  sonnet: vscode.LanguageModelChat,
  outerStream: vscode.ChatResponseStream,
  token: vscode.CancellationToken
) {
  await chatUtils.sendChatParticipantRequest(
    { content: step },
    chatCtx,
    {
      prompt: 'You are a coding sub-agent. Execute the task. Think step-by-step.',
      model: sonnet,
      tools: vscode.lm.tools.filter(t => t.tags?.includes('code')),
      responseStreamOptions: { stream: outerStream } // pipe back
    },
    token
  );
}
```

### 4.6 · Main orchestrator (`extension.ts`)

```ts
import { getModel } from './models';
import { createPlan } from './planner';
import { runStep } from './executor';

export function activate(ctx: vscode.ExtensionContext) {
  const deepagent = vscode.chat.createChatParticipant('multiAgent.deepagent', handler);
  ctx.subscriptions.push(deepagent);
}

const handler: vscode.ChatRequestHandler = async (req, chatCtx, stream, token) => {
  const opus = await getModel('claude-4-opus');
  const plan = await createPlan(opus, req.prompt, token);
  stream.markdown(`### 📝 Plan\n${plan.map((s, i) => `${i + 1}. ${s}`).join('\n')}\n`);
  const sonnet = await getModel('claude-4-sonnet');
  for (const step of plan) {
    stream.markdown(`---\n**Executing:** ${step}\n`);
    await runStep(step, chatCtx, sonnet, stream, token);
  }
  return {};
};
```

### 4.7 · **Tool strategy (v0)**

We **do not implement custom Language‑Model Tools**. Sonnet runs in agent‑mode and automatically receives the full set of VS Code built‑ins:

* `fileSystem.readFile`, `writeFile`, `applyEdit`, `createDiff`
* `workspace.search`, `symbolSearch`
* `terminal.runCommand`, `tasks.run`

VS Code prompts the user before destructive actions (file changes, shell commands), ensuring safety with zero extra code.

---

## 5 · Testing matrix

| Scenario                                        | Expected                                                                         | Notes                                                           |
| ----------------------------------------------- | -------------------------------------------------------------------------------- | --------------------------------------------------------------- |
| User asks simple Q (“What does function X do?”) | Opus may produce *0 or 1* steps → Sonnet answers.                                | Verify plan parser tolerates single-line plan.                  |
| Plan with code edits                            | Sonnet calls `editTool`; VS Code shows diff; user accepts → file modified.       | Ensure outer stream waits until diff accepted before next step. |
| Tool failure (regex no match)                   | Tool throws → Sonnet sees error, retries or informs user.                        | Confirm error text is streamed.                                 |
| Model unavailable                               | Graceful error: “Claude-4-Opus not configured. Configure Copilot or BYOM model.” | Use try/catch around `getModel`.                                |

Automate with `vscode-test` + stubbed models (dummy echo provider) for CI.

---

## 6 · User-facing docs (README excerpt)

```
### Usage
1. Open the Copilot Chat view (⌥⇧⌘I / Alt-Shift-Cmd-I).
2. Type `@deepagent Add a new command to export data as JSON and tests`.
3. Watch the plan appear, then each step execute.
4. Review proposed diffs and approve to apply.
```

---

## 7 · Milestones & effort

| Milestone | Deliverable                                                | Est. hrs     |
| --------- | ---------------------------------------------------------- | ------------ |
| M1        | Scaffold extension, register @deepagent participant, dummy echo | 2            |
| M2        | Implement model selection + planner (Opus)                 | 4            |
| M3        | Implement `sendChatParticipantRequest` executor loop       | 4            |
| M4        | Build and register workspaceSearch tool                    | 3            |
| M5        | Add editTool + diff approval                               | 4            |
| M6        | Package, README, CI tests                                  | 3            |
| **Total** | —                                                          | **\~20 hrs** |

---

## 8 · Future backlog

* Parallel execution of independent steps (Promise.all).
* Context summarization for long-running sessions.
* Config UI: let user re-map models (Gemini 1.5 Pro, local Ollama, etc.).
* Additional tools: refactor, Lint fix, stack-overflow offline search.

---

Hand this document to the coding agent; each code block is ready to paste into the new project. Happy hacking!

---

## 9 · References & Further Reading

| Topic                                           | Documentation / Source                                                                                                                                                           |
| ----------------------------------------------- | -------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| Chat Extension API                              | [https://code.visualstudio.com/api/extension-guides/chat](https://code.visualstudio.com/api/extension-guides/chat)                                                               |
| Language Model API                              | [https://code.visualstudio.com/api/extension-guides/language-model](https://code.visualstudio.com/api/extension-guides/language-model)                                           |
| Language Model Tools (agent‑mode)               | [https://code.visualstudio.com/api/extension-guides/language-model#language-model-tools](https://code.visualstudio.com/api/extension-guides/language-model#language-model-tools) |
| `@vscode/chat-extension-utils` helper library   | [https://github.com/microsoft/vscode-chat-extension-utils](https://github.com/microsoft/vscode-chat-extension-utils)                                                             |
| Example multi‑step agent extension – **Cline**  | [https://github.com/va-cline/cline](https://github.com/va-cline/cline)                                                                                                           |
| Example fork with multiple modes – **Roo Code** | [https://github.com/roocode/roocode](https://github.com/roocode/roocode)                                                                                                         |
| Example hybrid agent – **Kilo Code**            | [https://github.com/kilocodedev/kilo-code](https://github.com/kilocodedev/kilo-code)                                                                                             |
| Open‑source assistant framework – **Continue**  | [https://github.com/continue-repl/continue](https://github.com/continue-repl/continue)                                                                                           |
| Yeoman VS Code extension generator              | [https://github.com/microsoft/vscode-generator-code](https://github.com/microsoft/vscode-generator-code)                                                                         |
| VS Code Testing API (for `vscode-test`)         | [https://code.visualstudio.com/api/working-with-extensions/testing](https://code.visualstudio.com/api/working-with-extensions/testing)                                           |
| VS Code Workspace Edit API                      | [https://code.visualstudio.com/api/references/vscode-api#WorkspaceEdit](https://code.visualstudio.com/api/references/vscode-api#WorkspaceEdit)                                   |
| VS Code Terminal API                            | [https://code.visualstudio.com/api/references/vscode-api#Terminal](https://code.visualstudio.com/api/references/vscode-api#Terminal)                                             |
| Copilot environments & model IDs                | [https://github.com/microsoft/vscode-copilot-release-notes](https://github.com/microsoft/vscode-copilot-release-notes)                                                           |

> These links cover all APIs, helper libraries, and reference projects mentioned in the plan, so the implementation agent can quickly look up details when needed.
