# Multi-Agent Orchestrator

A VS Code Chat extension that implements a two-phase **Plan-and-Execute** workflow using Claude models.

## Overview

This extension exposes a chat participant **@deepagent** that:

1. **Phase 1 (Plan):** Uses Claude 4 Opus to decompose user prompts into numbered steps
2. **Phase 2 (Execute):** Iterates through each step using Claude 4 Sonnet in agent mode with built-in VS Code tools

## Features

- **@deepagent** chat participant for multi-step task orchestration
- Plan generation using Claude 4 Opus
- Step execution using Claude 4 Sonnet with access to VS Code's built-in tools:
  - File system operations (`readFile`, `writeFile`, `applyEdit`, `createDiff`)
  - Workspace search and symbol search
  - Terminal and task execution
- Streaming response for real-time feedback
- Built-in safety through VS Code's confirmation UI for destructive actions

## Requirements

- VS Code 1.85.0 or higher
- GitHub Copilot or other language model provider configured
- Claude 4 models (Opus and Sonnet) available through your language model provider

## Usage

1. Open the Copilot Chat view (⌥⇧⌘I / Alt-Shift-Cmd-I)
2. Type `@deepagent` followed by your request
3. Watch the plan appear, then each step execute automatically
4. Review and approve any proposed file changes or terminal commands

### Example

```
@deepagent Add a new command to export data as JSON and include comprehensive tests
```

The extension will:
1. Generate a plan using Opus (e.g., "1. Create export function 2. Add command registration 3. Write tests")
2. Execute each step using Sonnet with access to VS Code tools
3. Stream results back to the chat interface

## Development

### Building

```bash
npm install
npm run compile
```

### Package Structure

```
src/
├── extension.ts    # Main orchestrator and activation
├── models.ts       # Claude model selection helper
├── planner.ts      # Plan generation with Opus
└── executor.ts     # Step execution with Sonnet
```

## Architecture

The extension follows a simple orchestration pattern:

```
User → @deepagent → Opus (plan) → for each step { Sonnet (execute) } → stream results
```

- **Planning**: Opus receives the user prompt and generates a numbered action plan
- **Execution**: Each step is sent to Sonnet running in agent mode with access to VS Code's Language Model Tools
- **Streaming**: All responses are streamed back through the chat interface for real-time feedback

## Safety

File writes and shell commands automatically trigger VS Code's built-in confirmation dialogs, ensuring user control over destructive actions.

## License

MIT