# Multi-Agent Orchestrator

A VS Code Chat extension that implements a two-phase **Plan-and-Execute** workflow using Claude models. Now supports both **Chat Participant mode** and **MCP Server mode** for maximum flexibility.

## Overview

This extension provides two ways to access Deep Agent functionality:

### Chat Participant Mode (`@deepagent`)
- Use `@deepagent` in VS Code Chat (Ask mode)
- Interactive conversation style with streaming responses
- Full visibility into planning and execution process

### MCP Server Mode (Agent/Edit Mode Support)
- Provides MCP tools for VS Code Agent mode and Edit mode
- Autonomous execution with built-in VS Code tools
- Can be combined with other MCP servers and tools

Both modes implement the same core workflow:

1. **Phase 1 (Plan):** Uses Claude 4 Opus to decompose user prompts into numbered steps
2. **Phase 2 (Execute):** Iterates through each step using Claude 4 Sonnet in agent mode with built-in VS Code tools

## Features

### Chat Participant Features
- **@deepagent** chat participant for multi-step task orchestration in Ask mode
- Interactive planning and execution with streaming responses
- Full visibility into the planning and execution process

### MCP Server Features
- **MCP tools** for Agent mode and Edit mode support
- `deepagent_plan` - Create detailed action plans using <PERSON>
- `deepagent_execute_step` - Execute individual steps using Claude Sonnet
- `deepagent_plan_and_execute` - Complete workflow automation
- Integration with VS Code's autonomous Agent mode

### Core Capabilities
- Plan generation using Claude 4 Opus
- Step execution using Claude 4 Sonnet with access to VS Code's built-in tools:
  - File system operations (`readFile`, `writeFile`, `applyEdit`, `createDiff`)
  - Workspace search and symbol search
  - Terminal and task execution
- Streaming response for real-time feedback
- Built-in safety through VS Code's confirmation UI for destructive actions

## Requirements

- VS Code 1.85.0 or higher
- GitHub Copilot or other language model provider configured
- Claude 4 models (Opus and Sonnet) available through your language model provider

## Usage

### Chat Participant Mode (Ask Mode)

1. Open the Copilot Chat view (⌥⇧⌘I / Alt-Shift-Cmd-I)
2. Type `@deepagent` followed by your request
3. Watch the plan appear, then each step execute automatically
4. Review and approve any proposed file changes or terminal commands

**Example:**
```
@deepagent Add a new command to export data as JSON and include comprehensive tests
```

### MCP Server Mode (Agent/Edit Mode)

1. Set up the MCP server (see [MCP_SETUP.md](MCP_SETUP.md) for detailed instructions)
2. Use Agent mode or Edit mode in VS Code
3. Access Deep Agent tools via the tools picker or direct reference:
   - `#deepagent_plan` - Create action plans
   - `#deepagent_execute_step` - Execute individual steps
   - `#deepagent_plan_and_execute` - Complete workflow

**Example:**
```
Use deepagent_plan_and_execute to "Refactor the authentication system to use OAuth"
```

### Workflow

Both modes follow the same process:
1. Generate a plan using Opus (e.g., "1. Create export function 2. Add command registration 3. Write tests")
2. Execute each step using Sonnet with access to VS Code tools
3. Stream results back to the interface

## Installation

### Basic Setup (Chat Participant Only)
1. Clone this repository
2. Run `npm install`
3. Run `npm run compile`
4. Press F5 to launch a new VS Code window with the extension loaded
5. Open the Copilot Chat view and try `@deepagent`

### Full Setup (Chat Participant + MCP Server)
1. Follow steps 1-4 above
2. Configure the MCP server following the [MCP Setup Guide](MCP_SETUP.md)
3. Use `@deepagent` in Ask mode OR use Deep Agent tools in Agent/Edit mode

## Development

### Building

```bash
npm install
npm run compile
```

### Package Structure

```
src/
├── extension.ts     # Main orchestrator and activation
├── models.ts        # Claude model selection helper
├── planner.ts       # Plan generation with Opus
├── executor.ts      # Step execution with Sonnet
├── mcp-server.ts    # MCP server implementation
└── mcp-launcher.ts  # Standalone MCP server launcher
```

## Architecture

The extension follows a simple orchestration pattern:

```
User → @deepagent → Opus (plan) → for each step { Sonnet (execute) } → stream results
```

- **Planning**: Opus receives the user prompt and generates a numbered action plan
- **Execution**: Each step is sent to Sonnet running in agent mode with access to VS Code's Language Model Tools
- **Streaming**: All responses are streamed back through the chat interface for real-time feedback

## Safety

File writes and shell commands automatically trigger VS Code's built-in confirmation dialogs, ensuring user control over destructive actions.

## License

MIT