# Deep Agent MCP Server Launcher (PowerShell)
# This script helps resolve Node.js path issues

Write-Host "Starting Deep Agent MCP Server..." -ForegroundColor Green

# Try different Node.js paths
$nodePaths = @(
    "C:\Program Files\nodejs\node.exe",
    "C:\Program Files (x86)\nodejs\node.exe",
    "$env:USERPROFILE\AppData\Roaming\npm\node.exe",
    "node"
)

$nodeFound = $false

foreach ($nodePath in $nodePaths) {
    if ($nodePath -eq "node") {
        # Try using node from PATH
        try {
            $nodeVersion = & node --version 2>$null
            if ($LASTEXITCODE -eq 0) {
                Write-Host "Using Node.js from PATH: $nodeVersion" -ForegroundColor Yellow
                & node out\mcp-launcher.js
                $nodeFound = $true
                break
            }
        } catch {
            continue
        }
    } elseif (Test-Path $nodePath) {
        Write-Host "Using Node.js from: $nodePath" -ForegroundColor Yellow
        & $nodePath out\mcp-launcher.js
        $nodeFound = $true
        break
    }
}

if (-not $nodeFound) {
    Write-Host "Error: Node.js not found in any of the expected locations" -ForegroundColor Red
    Write-Host "Please install Node.js or update the script with the correct path" -ForegroundColor Red
    exit 1
}
