import * as vscode from 'vscode';
import { getModel } from './models';
import { createPlan } from './planner';
import { runStep } from './executor';

export function activate(ctx: vscode.ExtensionContext): void {
  console.log('🚀 Multi-Agent Orchestrator: Extension activating...');
  
  try {
    // Register a test command first
    const testCommand = vscode.commands.registerCommand('deepagent.test', () => {
      vscode.window.showInformationMessage('Deep Agent extension is active!');
      console.log('🧪 Test command executed - extension is active');
    });
    ctx.subscriptions.push(testCommand);
    
    // Register the chat participant with explicit error handling
    console.log('📝 Registering chat participant with ID: deepagent');
    
    try {
      const participant = vscode.chat.createChatParticipant('deepagent', handler);
      ctx.subscriptions.push(participant);
      
      console.log('✅ Chat participant created successfully');
      console.log('📋 Participant object:', typeof participant);
      
    } catch (participantError) {
      console.error('❌ Failed to create chat participant:', participantError);
      throw participantError;
    }
    
    console.log('✅ Multi-Agent Orchestrator: Chat participant registered as @deepagent');
    console.log('📋 Participant details:', {
      id: 'deepagent'
    });
    
    console.log('✅ Multi-Agent Orchestrator: Chat participant registered as @deepagent');
    console.log('📋 Extension context:', {
      extensionPath: ctx.extensionPath,
      subscriptions: ctx.subscriptions.length
    });
    
    // Show a notification that the extension is active
    vscode.window.showInformationMessage('🤖 Deep Agent extension activated! Try @deepagent in Chat.');
    
    // Debug: Try to see what chat participants are available
    setTimeout(async () => {
      try {
        console.log('🔍 Checking chat environment...');
        // Just log that we're checking - the actual API might not expose participant lists
        console.log('🔍 Extension should now be available as @deepagent in chat');
      } catch (debugError) {
        console.log('🔍 Debug check completed with some limitations:', debugError);
      }
    }, 2000);
    
  } catch (error) {
    console.error('❌ Failed to activate extension:', error);
    vscode.window.showErrorMessage(`Failed to activate Multi-Agent Orchestrator: ${error}`);
  }
}

const handler: vscode.ChatRequestHandler = async (req, chatCtx, stream, token) => {
  console.log('🎯🎯🎯 HANDLER CALLED! 🎯🎯🎯');
  console.log('🎯 Handler called with prompt:', req.prompt);
  console.log('📋 Chat context:', {
    history: chatCtx.history.length,
    command: req.command
  });
  
  // Immediate feedback to show the participant is working
  stream.markdown('✅ **@deepagent activated!** Starting multi-agent workflow...\n\n');
  
  // Add a simple test response first
  stream.markdown('🧪 **Test Response:** Handler is working! You said: ' + req.prompt + '\n\n');
  
  try {
    stream.markdown('🔍 **Starting Multi-Agent Orchestrator...**\n\n');
    
    // Phase 1: Plan with Opus
    console.log('📝 Phase 1: Getting Opus model...');
    stream.markdown('📝 **Phase 1:** Getting planning model...\n');
    const opus = await getModel('claude-opus-4');
    console.log('✅ Opus model obtained:', opus.id);
    
    stream.markdown('🧠 **Planning with Opus...**\n');
    console.log('🧠 Creating plan...');
    const plan = await createPlan(opus, req.prompt, token);
    console.log('📋 Plan created:', plan);
    
    // Display the plan
    stream.markdown(`### 📝 Plan\n${plan.map((s, i) => `${i + 1}. ${s}`).join('\n')}\n`);
    
    // Phase 2: Execute each step with Sonnet
    console.log('⚡ Phase 2: Getting Sonnet model...');
    stream.markdown('⚡ **Phase 2:** Getting execution model...\n');
    const sonnet = await getModel('claude-sonnet-4');
    console.log('✅ Sonnet model obtained:', sonnet.id);
    
    for (let i = 0; i < plan.length; i++) {
      const step = plan[i];
      console.log(`🔄 Executing step ${i + 1}:`, step);
      stream.markdown(`---\n**Executing Step ${i + 1}:** ${step}\n`);
      await runStep(step, chatCtx, sonnet, stream, token);
      console.log(`✅ Step ${i + 1} completed`);
    }
    
    console.log('🎉 All steps completed successfully');
    stream.markdown('\n🎉 **All steps completed!**\n');
    
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
    console.error('❌ Error in handler:', error);
    console.error('❌ Error stack:', error instanceof Error ? error.stack : 'No stack trace');
    stream.markdown(`❌ **Error:** ${errorMessage}`);
  }
  
  return {};
};

export function deactivate(): void {
  console.log('🛑 Multi-Agent Orchestrator: Extension deactivating...');
  // Cleanup if needed
}