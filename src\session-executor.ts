import * as vscode from 'vscode';
import { getModel } from './models';
import { runStep } from './executor';

export interface ExecutionProgress {
  type: 'output' | 'tool_call' | 'file_change' | 'completion' | 'error';
  content?: string;
  tool?: string;
  args?: any;
  file?: string;
  operation?: string;
  timestamp: number;
  sessionId: string;
}

export interface SessionExecutorOptions {
  taskDescription: string;
  onProgress?: (progress: ExecutionProgress) => void;
  maxHistoryLength?: number;
  toolInvocationToken?: vscode.ChatParticipantToolToken;
}

/**
 * Session-isolated executor that maintains its own context and session ID
 * while using direct VS Code tool access for file operations.
 */
export class SessionIsolatedExecutor {
  private sessionId: string;
  private taskDescription: string;
  private history: Array<{
    type: 'input' | 'output' | 'tool_call';
    content: string;
    timestamp: number;
  }> = [];
  private createdAt: number;
  private lastActivity: number;
  private onProgress?: (progress: ExecutionProgress) => void;
  private maxHistoryLength: number;
  private toolInvocationToken?: vscode.ChatParticipantToolToken;

  constructor(options: SessionExecutorOptions) {
    this.sessionId = `session-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
    this.taskDescription = options.taskDescription;
    this.onProgress = options.onProgress;
    this.maxHistoryLength = options.maxHistoryLength || 20;
    this.toolInvocationToken = options.toolInvocationToken;
    this.createdAt = Date.now();
    this.lastActivity = Date.now();

    console.log(`🆕 SessionExecutor: Created ${this.sessionId} for task: ${this.taskDescription}`);
  }

  /**
   * Execute a step with session-isolated context
   */
  async executeStep(step: string, context?: string): Promise<string> {
    console.log(`⚡ SessionExecutor ${this.sessionId}: Executing step: ${step}`);
    
    this.lastActivity = Date.now();

    // Report progress
    this.reportProgress({
      type: 'output',
      content: `Starting step: ${step}`,
      timestamp: Date.now(),
      sessionId: this.sessionId
    });

    try {
      // Build session-specific prompt with context isolation
      const sessionPrompt = this.buildSessionPrompt(step, context);

      // Execute step with session context and direct tool access
      const result = await this.executeWithDirectToolAccess(sessionPrompt);

      // Update session history
      this.addToHistory('input', step);
      this.addToHistory('output', result);

      // Report completion
      this.reportProgress({
        type: 'completion',
        content: result,
        timestamp: Date.now(),
        sessionId: this.sessionId
      });

      console.log(`✅ SessionExecutor ${this.sessionId}: Step completed, ${result.length} characters`);

      return result;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      
      // Report error
      this.reportProgress({
        type: 'error',
        content: errorMessage,
        timestamp: Date.now(),
        sessionId: this.sessionId
      });

      console.error(`❌ SessionExecutor ${this.sessionId}: Error:`, error);
      throw error;
    }
  }

  /**
   * Execute multiple steps in sequence within this session
   */
  async executeSteps(steps: string[]): Promise<string[]> {
    const results: string[] = [];
    
    for (let i = 0; i < steps.length; i++) {
      const step = steps[i];
      console.log(`📋 SessionExecutor ${this.sessionId}: Step ${i + 1}/${steps.length}`);
      
      const result = await this.executeStep(step);
      results.push(result);
    }

    return results;
  }

  /**
   * Get session information and statistics
   */
  getSessionInfo() {
    return {
      sessionId: this.sessionId,
      taskDescription: this.taskDescription,
      createdAt: this.createdAt,
      lastActivity: this.lastActivity,
      duration: Date.now() - this.createdAt,
      historyLength: this.history.length,
      isActive: Date.now() - this.lastActivity < 300000 // 5 minutes
    };
  }

  /**
   * Clean up session resources
   */
  dispose(): void {
    const duration = Date.now() - this.createdAt;
    console.log(`🗑️ SessionExecutor: Disposing ${this.sessionId} after ${duration}ms`);
    
    // Clear history to free memory
    this.history = [];
    
    // Report final progress
    this.reportProgress({
      type: 'completion',
      content: 'Session disposed',
      timestamp: Date.now(),
      sessionId: this.sessionId
    });
  }

  private buildSessionPrompt(step: string, context?: string): string {
    // Get recent history for context (last 3 interactions)
    const recentHistory = this.history.slice(-6); // 3 input/output pairs
    const historyContext = recentHistory
      .filter(h => h.type === 'output')
      .map(h => h.content.substring(0, 200) + '...')
      .join('\n');

    // Clean up task description to remove confusing mode references
    const cleanTaskDescription = this.taskDescription
      .replace(/^Ask mode request:\s*/i, '')
      .replace(/^Agent mode request:\s*/i, '')
      .replace(/^MCP request:\s*/i, '')
      .trim();

    // Build a clean, focused prompt for the AI
    return `${step}

${context ? `Context: ${context}\n\n` : ''}${historyContext ? `Previous work in this session:\n${historyContext}\n\n` : ''}Task goal: ${cleanTaskDescription}

Execute this step using available tools as needed. Focus on the specific step requested while keeping the overall task goal in mind.`;
  }

  private async executeWithDirectToolAccess(prompt: string): Promise<string> {
    // Get Claude Sonnet model with direct tool access
    console.log(`🔍 SessionExecutor ${this.sessionId}: Getting Claude Sonnet model...`);
    const sonnet = await getModel('claude-sonnet-4');
    console.log(`✅ SessionExecutor ${this.sessionId}: Got Claude Sonnet model`);

    // Create a stream to capture output and monitor tool usage
    let output = '';
    const monitoredStream = {
      markdown: (text: string) => {
        console.log(`📝 SessionExecutor ${this.sessionId}: Output:`, text.substring(0, 100) + '...');
        output += text;
        
        // Report progress for streaming output
        this.reportProgress({
          type: 'output',
          content: text,
          timestamp: Date.now(),
          sessionId: this.sessionId
        });
      },
      button: () => {},
      filetree: () => {},
      anchor: () => {},
      progress: () => {},
      reference: () => {},
      push: () => {}
    };

    // Create a minimal chat context
    const mockChatContext = {
      history: []
    } as vscode.ChatContext;

    console.log(`🚀 SessionExecutor ${this.sessionId}: Executing with direct tool access...`);
    const startTime = Date.now();

    // Execute using the same runStep function that has direct tool access
    await runStep(prompt, mockChatContext, sonnet, monitoredStream as any, new vscode.CancellationTokenSource().token, this.toolInvocationToken);

    const duration = Date.now() - startTime;
    console.log(`✅ SessionExecutor ${this.sessionId}: Execution completed in ${duration}ms, captured ${output.length} characters`);

    return output || `Executed step in session ${this.sessionId}: ${prompt.substring(0, 100)}...`;
  }

  private addToHistory(type: 'input' | 'output' | 'tool_call', content: string): void {
    this.history.push({
      type,
      content,
      timestamp: Date.now()
    });

    // Trim history if it gets too long
    if (this.history.length > this.maxHistoryLength) {
      this.history = this.history.slice(-this.maxHistoryLength);
    }
  }

  private reportProgress(progress: ExecutionProgress): void {
    if (this.onProgress) {
      this.onProgress(progress);
    }
  }
}
