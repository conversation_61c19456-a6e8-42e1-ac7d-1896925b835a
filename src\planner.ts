import * as vscode from 'vscode';

export async function createPlan(
  model: vscode.LanguageModelChat, 
  userPrompt: string, 
  token: vscode.CancellationToken
): Promise<string[]> {
  console.log('🧠 Creating plan with model:', model.id);
  console.log('📝 User prompt:', userPrompt);
  
  try {
    const resp = await model.sendRequest(
      [
        vscode.LanguageModelChatMessage.Assistant("You are a senior software architect. Produce an ordered action plan (1.,2.,3.) and nothing else."),
        vscode.LanguageModelChatMessage.User(userPrompt)
      ],
      {},
      token
    );
    
    console.log('📡 Received response from model, processing...');
    
    let text = '';
    for await (const chunk of resp.text) {
      text += chunk;
    }
    
    console.log('📋 Raw plan text:', text);
    
    const steps = text.split(/\n\d+\./).filter(Boolean).map((s: string) => s.trim());
    console.log('📋 Parsed plan steps:', steps);
    
    return steps;
  } catch (error) {
    console.error('❌ Error in createPlan:', error);
    throw error;
  }
}