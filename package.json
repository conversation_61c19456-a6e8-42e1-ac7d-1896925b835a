{"name": "multi-agent-orchestrator", "displayName": "Multi-Agent Orchestrator", "description": "VS Code Chat extension with @deepagent participant that plans with Opus and executes with Sonnet", "version": "0.0.1", "engines": {"vscode": "^1.85.0"}, "categories": ["AI", "Chat Participants"], "activationEvents": ["onStartupFinished"], "main": "./out/extension.js", "contributes": {"chatParticipants": [{"id": "deepagent", "name": "deepagent", "fullName": "Multi-Agent Orchestrator", "description": "Plans with Opus and executes with Sonnet."}], "commands": [{"command": "deepagent.test", "title": "Test Deep Agent"}]}, "scripts": {"vscode:prepublish": "npm run compile", "compile": "tsc -p ./", "watch": "tsc -watch -p ./"}, "enabledApiProposals": [], "devDependencies": {"@types/vscode": "^1.85.0", "@types/node": "^18.x", "typescript": "^5.3.0"}, "dependencies": {"@vscode/chat-extension-utils": "latest"}}