{"name": "multi-agent-orchestrator", "displayName": "Multi-Agent Orchestrator", "description": "VS Code Chat extension with @deepagent participant that plans with Opus and executes with Sonnet. Also provides MCP server for Agent mode.", "version": "0.0.1", "engines": {"vscode": "^1.85.0"}, "categories": ["AI", "Chat Participants"], "activationEvents": ["onStartupFinished"], "main": "./out/extension.js", "contributes": {"chatParticipants": [{"id": "deepagent", "name": "deepagent", "fullName": "Multi-Agent Orchestrator", "description": "Plans with Opus and executes with Sonnet."}], "commands": [{"command": "deepagent.test", "title": "Test Deep Agent"}], "configuration": {"title": "Deep Agent", "properties": {"deepagent.mcp.enabled": {"type": "boolean", "default": true, "description": "Enable MCP server for Agent mode support"}, "deepagent.mcp.port": {"type": "number", "default": 3001, "description": "Port for the MCP server"}}}}, "scripts": {"vscode:prepublish": "npm run compile", "compile": "tsc -p ./", "watch": "tsc -watch -p ./"}, "enabledApiProposals": [], "devDependencies": {"@types/node": "^18.x", "@types/vscode": "^1.85.0", "typescript": "^5.3.0"}, "dependencies": {"@modelcontextprotocol/sdk": "^1.13.1", "@vscode/chat-extension-utils": "latest"}}