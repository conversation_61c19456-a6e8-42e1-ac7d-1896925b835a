#!/usr/bin/env node

/**
 * Demo script showing MCP server capabilities
 * This demonstrates both standalone mode (mock) and IPC bridge mode (real VS Code APIs)
 */

const { spawn } = require('child_process');
const path = require('path');

async function demonstrateMCPServer() {
  console.log('🎭 Deep Agent MCP Server Demo');
  console.log('=====================================\n');
  
  const serverPath = path.join(__dirname, 'out', 'mcp-launcher.js');
  
  console.log('🚀 Starting MCP Server...');
  
  // Start the MCP server
  const server = spawn('node', [serverPath], {
    stdio: ['pipe', 'pipe', 'pipe'],
    cwd: __dirname
  });
  
  let output = '';
  let errorOutput = '';
  
  server.stdout.on('data', (data) => {
    output += data.toString();
  });
  
  server.stderr.on('data', (data) => {
    errorOutput += data.toString();
  });
  
  // Give the server a moment to start
  await new Promise(resolve => setTimeout(resolve, 2000));
  
  console.log('📋 Listing available tools...\n');
  
  // Send a list tools request
  const listToolsRequest = {
    jsonrpc: '2.0',
    id: 1,
    method: 'tools/list'
  };
  
  server.stdin.write(JSON.stringify(listToolsRequest) + '\n');
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  console.log('🧠 Testing planning tool...\n');
  
  // Test the plan tool
  const planRequest = {
    jsonrpc: '2.0',
    id: 2,
    method: 'tools/call',
    params: {
      name: 'deepagent_plan',
      arguments: {
        task: 'Create a TypeScript function that validates email addresses'
      }
    }
  };
  
  server.stdin.write(JSON.stringify(planRequest) + '\n');
  await new Promise(resolve => setTimeout(resolve, 2000));
  
  console.log('⚡ Testing execution tool...\n');
  
  // Test the execute tool
  const executeRequest = {
    jsonrpc: '2.0',
    id: 3,
    method: 'tools/call',
    params: {
      name: 'deepagent_execute_step',
      arguments: {
        step: 'Create a TypeScript function with email validation regex',
        context: 'Building an email validator function'
      }
    }
  };
  
  server.stdin.write(JSON.stringify(executeRequest) + '\n');
  await new Promise(resolve => setTimeout(resolve, 2000));
  
  console.log('🚀 Testing complete workflow...\n');
  
  // Test the plan and execute tool
  const planAndExecuteRequest = {
    jsonrpc: '2.0',
    id: 4,
    method: 'tools/call',
    params: {
      name: 'deepagent_plan_and_execute',
      arguments: {
        task: 'Create a simple utility function',
        max_steps: 3
      }
    }
  };
  
  server.stdin.write(JSON.stringify(planAndExecuteRequest) + '\n');
  await new Promise(resolve => setTimeout(resolve, 3000));
  
  // Clean shutdown
  server.kill('SIGTERM');
  
  console.log('\n=====================================');
  console.log('📊 Demo Results:');
  console.log('=====================================\n');
  
  if (errorOutput.includes('Deep Agent MCP Server is running')) {
    console.log('✅ MCP Server: Started successfully');
  } else {
    console.log('❌ MCP Server: Failed to start');
  }
  
  if (output.includes('deepagent_plan')) {
    console.log('✅ Tools: All three tools registered correctly');
  } else {
    console.log('❌ Tools: Registration failed');
  }
  
  if (errorOutput.includes('Could not connect to VS Code extension IPC server')) {
    console.log('⚠️  Mode: Standalone (using mock implementations)');
    console.log('   💡 To use real VS Code APIs, start the VS Code extension first');
  } else if (errorOutput.includes('Connected to VS Code extension IPC server')) {
    console.log('✅ Mode: IPC Bridge connected (using real VS Code APIs)');
  }
  
  console.log('\n📝 Sample Responses:');
  console.log('=====================================');
  
  // Parse and display some sample responses
  const responses = output.split('\n').filter(line => {
    try {
      const parsed = JSON.parse(line);
      return parsed.result && parsed.result.content;
    } catch {
      return false;
    }
  });
  
  if (responses.length > 0) {
    try {
      const planResponse = JSON.parse(responses[0]);
      if (planResponse.result && planResponse.result.content) {
        console.log('\n🧠 Plan Tool Response:');
        console.log(planResponse.result.content[0].text.substring(0, 200) + '...');
      }
    } catch (e) {
      console.log('Could not parse plan response');
    }
  }
  
  console.log('\n🎉 Demo completed!');
  console.log('\nNext steps:');
  console.log('1. Configure MCP client to use this server');
  console.log('2. Use Deep Agent tools in VS Code Agent mode');
  console.log('3. For full functionality, ensure VS Code extension is running');
}

demonstrateMCPServer().catch(console.error);
