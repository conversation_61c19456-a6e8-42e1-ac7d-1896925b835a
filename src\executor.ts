import * as vscode from 'vscode';

export async function runStep(
  step: string,
  chatCtx: vscode.ChatContext,
  sonnet: vscode.LanguageModelChat,
  outerStream: vscode.ChatResponseStream,
  token: vscode.CancellationToken
): Promise<void> {
  console.log('⚡ Running step with model:', sonnet.id);
  console.log('📝 Step to execute:', step);
  
  try {
    // Use the model directly to execute the step
    const messages = [
      vscode.LanguageModelChatMessage.Assistant('You are a coding sub-agent. Execute the task. Think step-by-step.'),
      vscode.LanguageModelChatMessage.User(step)
    ];

    console.log('🛠️ Available tools:', vscode.lm.tools.map(t => ({ name: t.name, description: t.description, tags: t.tags })));
    const codeTools = vscode.lm.tools.filter(t => t.tags?.includes('code'));
    console.log('🔧 Using code tools:', codeTools.map(t => t.name));

    const response = await sonnet.sendRequest(
      messages,
      {
        tools: codeTools
      },
      token
    );

    console.log('📡 Received response from Sonnet, streaming...');

    // Stream the response back
    for await (const chunk of response.text) {
      outerStream.markdown(chunk);
    }
    
    console.log('✅ Step execution completed');
  } catch (error) {
    console.error('❌ Error in runStep:', error);
    outerStream.markdown(`❌ **Error executing step:** ${error instanceof Error ? error.message : 'Unknown error'}\n`);
    throw error;
  }
}