import * as vscode from 'vscode';

export async function runStep(
  step: string,
  chatCtx: vscode.ChatContext,
  sonnet: vscode.LanguageModelChat,
  outerStream: vscode.ChatResponseStream,
  token: vscode.CancellationToken,
  toolInvocationToken?: vscode.ChatParticipantToolToken
): Promise<void> {
  console.log('⚡ Running step with model:', sonnet.id);
  console.log('📝 Step to execute:', step);

  try {
    // Get workspace path for absolute file paths
    const workspacePath = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath || process.cwd();
    console.log('📁 Workspace path:', workspacePath);

    // Use the model directly to execute the step
    const messages = [
      vscode.LanguageModelChatMessage.Assistant(`You are a coding assistant. You MUST use the available tools to complete tasks. When asked to create files, use copilot_createFile with ABSOLUTE file paths. The current workspace is: ${workspacePath}. For example, to create "rand.rs" use "${workspacePath}\\rand.rs" (Windows) or "${workspacePath}/rand.rs" (Unix). When asked to edit files, use copilot_insertEdit. Do not just describe what you would do - actually use the tools to do it.`),
      vscode.LanguageModelChatMessage.User(step)
    ];

    console.log('🛠️ Available tools:', vscode.lm.tools.map(t => ({ name: t.name, description: t.description, tags: t.tags })));

    // Try to find code-related tools with different strategies
    let codeTools = vscode.lm.tools.filter(t => t.tags?.includes('code'));

    // If no tools with 'code' tag, try other relevant tags
    if (codeTools.length === 0) {
      codeTools = vscode.lm.tools.filter(t =>
        t.tags?.some(tag => ['edit', 'file', 'workspace', 'search'].includes(tag)) ||
        t.name.toLowerCase().includes('edit') ||
        t.name.toLowerCase().includes('file') ||
        t.name.toLowerCase().includes('write')
      );
    }

    // If still no tools, use all available tools
    if (codeTools.length === 0) {
      console.log('⚠️ No code-specific tools found, using all available tools');
      codeTools = [...vscode.lm.tools];
    }

    console.log('🔧 Using code tools:', codeTools.map(t => t.name));

    const response = await sonnet.sendRequest(
      messages,
      {
        tools: codeTools
      },
      token
    );

    console.log('📡 Received response from Sonnet, streaming...');

    // Handle both text and tool calls from the stream
    let hasToolCalls = false;

    // Process the stream which contains both text and tool calls
    for await (const chunk of response.stream) {
      if (chunk instanceof vscode.LanguageModelTextPart) {
        // Stream text content
        outerStream.markdown(chunk.value);
      } else if (chunk instanceof vscode.LanguageModelToolCallPart) {
        // Handle tool call
        hasToolCalls = true;
        console.log(`🛠️ Executing tool: ${chunk.name} with input:`, chunk.input);
        outerStream.markdown(`\n🔧 **Using tool:** ${chunk.name}\n`);

        try {
          // Debug the tool invocation parameters
          console.log('🔍 Tool invocation debug:', {
            toolName: chunk.name,
            hasToolInvocationToken: !!toolInvocationToken,
            toolInvocationTokenType: typeof toolInvocationToken,
            inputKeys: Object.keys(chunk.input || {}),
            tokenCancelled: token.isCancellationRequested
          });

          // Execute the tool call with proper options
          const toolResult = await vscode.lm.invokeTool(chunk.name, {
            toolInvocationToken: toolInvocationToken,
            input: chunk.input
          }, token);
          console.log(`✅ Tool ${chunk.name} executed successfully`);

          // Stream tool result if it has content
          if (toolResult && typeof toolResult === 'string') {
            outerStream.markdown(`📝 **Result:** ${toolResult}\n`);
          } else if (toolResult && typeof toolResult === 'object' && 'content' in toolResult) {
            outerStream.markdown(`📝 **Result:** ${toolResult.content}\n`);
          }
        } catch (toolError) {
          console.error(`❌ Error executing tool ${chunk.name}:`, toolError);

          outerStream.markdown(`❌ **Tool error:** ${toolError instanceof Error ? toolError.message : 'Unknown tool error'}\n`);

          // As a last resort, just acknowledge the tool call
          outerStream.markdown(`⚠️ **Tool call details:**\n`);
          outerStream.markdown(`- **Tool:** ${chunk.name}\n`);
          outerStream.markdown(`- **Input:** \`${JSON.stringify(chunk.input)}\`\n`);
        }
      } else {
        // Handle unknown chunk types (future-proofing)
        console.log('🔍 Unknown chunk type:', typeof chunk);
      }
    }

    if (!hasToolCalls) {
      console.log('ℹ️ No tool calls were made by the model');
    }

    console.log('✅ Step execution completed');
  } catch (error) {
    console.error('❌ Error in runStep:', error);
    outerStream.markdown(`❌ **Error executing step:** ${error instanceof Error ? error.message : 'Unknown error'}\n`);
    throw error;
  }
}