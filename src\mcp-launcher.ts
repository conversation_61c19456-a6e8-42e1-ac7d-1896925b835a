#!/usr/bin/env node

/**
 * MCP Server Launcher for Deep Agent
 * 
 * This script launches the Deep Agent MCP server that can be used
 * with VS Code Agent mode and other MCP-compatible clients.
 * 
 * Usage:
 *   node out/mcp-launcher.js
 * 
 * The server communicates via stdio and provides tools for:
 * - deepagent_plan: Create action plans using Claude Opus
 * - deepagent_execute_step: Execute individual steps using Claude Sonnet
 * - deepagent_plan_and_execute: Complete workflow
 */

import { DeepAgentMCPServer } from './mcp-server.js';

async function main() {
  const server = new DeepAgentMCPServer();
  
  // Handle graceful shutdown
  process.on('SIGINT', async () => {
    console.error('🛑 Received SIGINT, shutting down MCP server...');
    await server.stop();
    process.exit(0);
  });

  process.on('SIGTERM', async () => {
    console.error('🛑 Received SIGTERM, shutting down MCP server...');
    await server.stop();
    process.exit(0);
  });

  try {
    await server.start();
    console.error('🚀 Deep Agent MCP Server is running...');
    
    // Keep the process alive
    process.stdin.resume();
  } catch (error) {
    console.error('❌ Failed to start MCP server:', error);
    process.exit(1);
  }
}

main().catch((error) => {
  console.error('❌ Unhandled error in MCP server:', error);
  process.exit(1);
});
